"""
Custom action to extract appointment reschedule slots.
This file handles the extraction of reschedule_appointment_id, reschedule_phone_number,
and new appointment datetime entities without overwriting each other's values.
"""

from typing import Any, Text, Dict, List
import logging
import re

from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet

logger = logging.getLogger(__name__)

class ActionExtractAppointmentRescheduleSlots(Action):
    """
    Custom action to extract reschedule-related entities from user messages
    without overwriting each other for the reschedule form.

    This action is designed to handle appointment_id, phone_number, date, and time
    entities for the reschedule workflow.
    """

    def name(self) -> Text:
        return "action_extract_appointment_reschedule_slots"

    def run(
        self,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: Dict[Text, Any]
    ) -> List[Dict[Text, Any]]:
        """
        Extract reschedule-related entities from the latest message
        and set the corresponding slots without overwriting each other.
        """
        # Get the latest message
        latest_message = tracker.latest_message

        # Initialize events list to return
        events = []

        # Get current slot values to avoid overwriting valid values
        current_appointment_id = tracker.get_slot("reschedule_appointment_id")
        current_phone_number = tracker.get_slot("reschedule_phone_number")
        current_new_datetime = tracker.get_slot("new_appointment_datetime")

        logger.debug(f"Current slots - appointment_id: {current_appointment_id}, "
                    f"phone_number: {current_phone_number}, new_datetime: {current_new_datetime}")

        # Extract entities from the latest message
        entities = latest_message.get('entities', [])
        logger.debug(f"Entities found in message: {entities}")

        # Process each entity
        for entity in entities:
            entity_type = entity.get('entity')
            entity_value = entity.get('value')

            if entity_type == 'appointment_id' and not current_appointment_id:
                logger.debug(f"Setting reschedule_appointment_id to: {entity_value}")
                events.append(SlotSet("reschedule_appointment_id", entity_value))

            elif entity_type == 'phone_number' and not current_phone_number:
                logger.debug(f"Setting reschedule_phone_number to: {entity_value}")
                events.append(SlotSet("reschedule_phone_number", entity_value))

            elif entity_type in ['date', 'time']:
                # Handle date/time entities for new appointment datetime
                logger.debug(f"Processing {entity_type} entity: {entity_value}")
                
                # Build new datetime string by combining existing and new values
                if current_new_datetime:
                    new_datetime = f"{current_new_datetime} {entity_value}".strip()
                else:
                    new_datetime = entity_value
                
                logger.debug(f"Setting new_appointment_datetime to: {new_datetime}")
                events.append(SlotSet("new_appointment_datetime", new_datetime))

        # Also check for appointment ID patterns in the text if no entity was found
        if not current_appointment_id and not any(e.get('entity') == 'appointment_id' for e in entities):
            message_text = latest_message.get('text', '')
            
            # Look for appointment ID patterns (numbers)
            id_patterns = [
                r'\b(\d{3,4})\b',  # 3-4 digit numbers
                r'(?:id|ID)\s*[:\-]?\s*(\d+)',  # "ID: 1234" or "id 1234"
                r'(?:appointment|booking)\s*[:\-]?\s*(\d+)',  # "appointment 1234"
            ]
            
            for pattern in id_patterns:
                match = re.search(pattern, message_text)
                if match:
                    potential_id = match.group(1)
                    logger.debug(f"Found potential appointment ID in text: {potential_id}")
                    events.append(SlotSet("reschedule_appointment_id", potential_id))
                    break

        # Check for phone number patterns if no entity was found
        if not current_phone_number and not any(e.get('entity') == 'phone_number' for e in entities):
            message_text = latest_message.get('text', '')
            
            # Look for phone number patterns
            phone_patterns = [
                r'\b(\d{10})\b',  # 10 digit numbers
                r'\b(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})\b',  # Various phone formats
                r'\+\d{1,3}[-.\s]?\d{10}',  # International format
            ]
            
            for pattern in phone_patterns:
                match = re.search(pattern, message_text)
                if match:
                    potential_phone = match.group(1) if match.groups() else match.group(0)
                    logger.debug(f"Found potential phone number in text: {potential_phone}")
                    events.append(SlotSet("reschedule_phone_number", potential_phone))
                    break

        logger.debug(f"Returning events: {events}")
        return events
