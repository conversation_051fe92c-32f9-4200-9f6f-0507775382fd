"""
Custom action to extract appointment verification slots.
This file handles the extraction of appointment_id and phone_number entities
without overwriting each other's values.
"""

from typing import Any, Text, Dict, List
import logging
import re

from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet

logger = logging.getLogger(__name__)

class ActionExtractAppointmentVerificationSlots(Action):
    """
    Custom action to extract appointment_id and phone_number entities
    from user messages without overwriting each other.

    This action is designed to fix the issue where providing a phone number
    would overwrite the appointment_id slot.
    """

    def name(self) -> Text:
        return "action_extract_appointment_verification_slots"

    def run(
        self,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: Dict[Text, Any]
    ) -> List[Dict[Text, Any]]:
        """
        Extract appointment_id and phone_number entities from the latest message
        and set the corresponding slots without overwriting each other.
        """
        # Get the latest message
        latest_message = tracker.latest_message

        # Initialize events list to return
        events = []

        # Get current slot values to avoid overwriting valid values
        current_appointment_id = tracker.get_slot("appointment_id")
        current_phone_number = tracker.get_slot("phone_number")

        # Log the current state for debugging
        logger.debug(f"Current slots before extraction - appointment_id: {current_appointment_id}, phone_number: {current_phone_number}")
        logger.debug(f"Latest message: {latest_message}")

        # Extract entities from the latest message
        entities = latest_message.get("entities", [])

        # Variables to track if we found new values
        new_appointment_id = None
        new_phone_number = None

        # Process each entity
        for entity in entities:
            entity_type = entity.get("entity")
            entity_value = entity.get("value")

            logger.debug(f"Processing entity: {entity_type} = {entity_value}")

            # Handle appointment_id entity
            if entity_type == "appointment_id" and entity_value:
                # Clean the appointment ID (extract only digits)
                cleaned_id = ''.join(filter(str.isdigit, str(entity_value)))
                if cleaned_id:
                    new_appointment_id = cleaned_id
                    logger.debug(f"Extracted appointment_id: {new_appointment_id}")

            # Handle phone_number entity
            elif entity_type == "phone_number" and entity_value:
                # Clean the phone number (extract only digits)
                cleaned_phone = ''.join(filter(str.isdigit, str(entity_value)))
                if cleaned_phone:
                    new_phone_number = cleaned_phone
                    logger.debug(f"Extracted phone_number: {new_phone_number}")

        # If no entities were found, try to extract from text
        if not entities:
            text = latest_message.get("text", "")
            logger.debug(f"No entities found, trying to extract from text: {text}")

            # Get the requested slot from the form
            requested_slot = tracker.get_slot("requested_slot")
            logger.debug(f"Currently requested slot: {requested_slot}")

            # Clean the text (remove all non-digit characters)
            cleaned_text = ''.join(filter(str.isdigit, text))

            # If we're specifically asking for appointment_id
            if requested_slot == "appointment_id" and not current_appointment_id:
                # For appointment ID, we expect shorter numbers (typically 1-6 digits)
                if cleaned_text and len(cleaned_text) <= 6:
                    new_appointment_id = cleaned_text
                    logger.debug(f"Extracted appointment_id from text based on requested slot: {new_appointment_id}")

            # If we're specifically asking for phone_number
            elif requested_slot == "phone_number" and not current_phone_number:
                # For phone numbers, we expect at least 10 digits
                if cleaned_text and len(cleaned_text) >= 10:
                    new_phone_number = cleaned_text
                    logger.debug(f"Extracted phone_number from text based on requested slot: {new_phone_number}")

            # If we're not sure what slot is being requested, try to infer from the input format
            elif not requested_slot:
                # If the input looks like a phone number (10+ digits)
                if cleaned_text and len(cleaned_text) >= 10:
                    new_phone_number = cleaned_text
                    logger.debug(f"Inferred phone_number from text format: {new_phone_number}")
                # If the input looks like an appointment ID (shorter number)
                elif cleaned_text and len(cleaned_text) <= 6:
                    new_appointment_id = cleaned_text
                    logger.debug(f"Inferred appointment_id from text format: {new_appointment_id}")

        # Set slots only if new values were found and don't overwrite existing values
        # unless they're being updated

        # For appointment_id
        if new_appointment_id:
            events.append(SlotSet("appointment_id", new_appointment_id))
            logger.debug(f"Setting appointment_id slot to: {new_appointment_id}")

        # For phone_number
        if new_phone_number:
            events.append(SlotSet("phone_number", new_phone_number))
            logger.debug(f"Setting phone_number slot to: {new_phone_number}")

        # Log the final events
        logger.debug(f"Returning events: {events}")

        return events
