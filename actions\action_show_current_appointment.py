"""
Action to show current appointment details during reschedule process.
This action displays the current appointment information to the user
before asking for confirmation to proceed with rescheduling.
"""

from typing import Any, Text, Dict, List
import logging

from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet

logger = logging.getLogger(__name__)

# Mock appointment database for demo purposes
# TODO: Replace with actual database queries when integrating with database
MOCK_APPOINTMENTS = {
    "1001": {
        "appointment_id": "1001",
        "service_type": "Physical Therapy & Comprehensive Pain Management",
        "appointment_date": "Monday, May 20, 2024",
        "appointment_time": "10:00 AM",
        "doctor_name": "Dr. <PERSON>",
        "phone_number": "9123456789",
        "status": "booked"
    },
    "1002": {
        "appointment_id": "1002",
        "service_type": "Weight Loss Programs",
        "appointment_date": "Wednesday, May 22, 2024",
        "appointment_time": "2:00 PM",
        "doctor_name": "Dr. <PERSON>",
        "phone_number": "9876543210",
        "status": "booked"
    },
    "1003": {
        "appointment_id": "1003",
        "service_type": "Neurological Rehabilitation",
        "appointment_date": "Friday, May 24, 2024",
        "appointment_time": "4:00 PM",
        "doctor_name": "Dr. <PERSON> Rodriguez",
        "phone_number": "9555666777",
        "status": "booked"
    },
    "1004": {
        "appointment_id": "1004",
        "service_type": "Orthopedic Rehabilitation",
        "appointment_date": "Tuesday, May 21, 2024",
        "appointment_time": "11:30 AM",
        "doctor_name": "Dr. James Wilson",
        "phone_number": "9444555666",
        "status": "booked"
    },
    "1005": {
        "appointment_id": "1005",
        "service_type": "Gynaecology Rehabilitation & Prenatal Care",
        "appointment_date": "Thursday, May 23, 2024",
        "appointment_time": "3:30 PM",
        "doctor_name": "Dr. Lisa Patel",
        "phone_number": "9333444555",
        "status": "booked"
    }
}

class ActionShowCurrentAppointment(Action):
    """
    Action to display current appointment details before rescheduling.
    """

    def name(self) -> Text:
        return "action_show_current_appointment"

    def run(
        self,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: Dict[Text, Any]
    ) -> List[Dict[Text, Any]]:
        """
        Show the current appointment details and ask for confirmation to reschedule.
        """
        appointment_id = tracker.get_slot("reschedule_appointment_id")
        phone_number = tracker.get_slot("reschedule_phone_number")
        
        logger.debug(f"Showing appointment details for ID: {appointment_id}, Phone: {phone_number}")
        
        if appointment_id and phone_number:
            # TODO: Replace with actual database query
            # For now, use mock data
            
            if appointment_id in MOCK_APPOINTMENTS:
                appointment = MOCK_APPOINTMENTS[appointment_id]
                
                # Verify phone number matches (for demo, we'll be lenient)
                # TODO: Add strict phone number verification with database
                
                dispatcher.utter_message(
                    text=f"Here are your current appointment details:\n\n"
                         f"📅 **Appointment ID:** {appointment_id}\n"
                         f"🏥 **Service:** {appointment['service_type']}\n"
                         f"📆 **Date & Time:** {appointment['appointment_date']} at {appointment['appointment_time']}\n"
                         f"👨‍⚕️ **Doctor:** {appointment['doctor_name']}\n\n"
                         f"Would you like to reschedule this appointment?"
                )
                
                # Store appointment details in slots for later use
                return [
                    SlotSet("mock_service_type", appointment['service_type']),
                    SlotSet("mock_appointment_datetime", f"{appointment['appointment_date']} at {appointment['appointment_time']}"),
                    SlotSet("mock_doctor_name", appointment['doctor_name'])
                ]
            else:
                # Default mock data for any other ID
                dispatcher.utter_message(
                    text=f"Here are your current appointment details:\n\n"
                         f"📅 **Appointment ID:** {appointment_id}\n"
                         f"🏥 **Service:** Physical Therapy & Comprehensive Pain Management\n"
                         f"📆 **Date & Time:** Monday, May 20, 2024 at 10:00 AM\n"
                         f"👨‍⚕️ **Doctor:** Dr. Sarah Johnson\n\n"
                         f"Would you like to reschedule this appointment?"
                )
                
                # Store default appointment details in slots
                return [
                    SlotSet("mock_service_type", "Physical Therapy & Comprehensive Pain Management"),
                    SlotSet("mock_appointment_datetime", "Monday, May 20, 2024 at 10:00 AM"),
                    SlotSet("mock_doctor_name", "Dr. Sarah Johnson")
                ]
        else:
            # This shouldn't happen if the form is working correctly
            dispatcher.utter_message(text="I need both your appointment ID and phone number to show your appointment details.")
            return []
