"""
Action to submit appointment cancellation after form completion.
This file handles the actual cancellation of the appointment in the database
after the user has confirmed.
"""

from typing import Any, Text, Dict, List
import logging
from datetime import datetime

from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet, FollowupAction

# Import database connection function from db_utils.py
from actions.db_utils import get_db_connection

logger = logging.getLogger(__name__)

class ActionSubmitAppointmentCancellation(Action):
    """
    Action to handle the actual cancellation of an appointment after form completion.

    This action is called after the appointment_cancellation_form is complete
    and the user has confirmed the cancellation. It updates the appointment status
    in the database and frees up the availability slot.
    """

    def name(self) -> Text:
        return "action_submit_appointment_cancellation"

    def run(
        self,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: Dict[Text, Any]
    ) -> List[Dict[Text, Any]]:
        """
        Cancel the appointment in the database and free up the availability slot.
        """
        # Get the appointment_id and phone_number from the tracker
        appointment_id = tracker.get_slot("appointment_id")
        phone_number = tracker.get_slot("phone_number")
        confirm_cancellation = tracker.get_slot("confirm_cancellation")

        logger.debug(f"ActionSubmitAppointmentCancellation: appointment_id={appointment_id}, phone_number={phone_number}, confirm_cancellation={confirm_cancellation}")

        # If the user didn't confirm cancellation, just clear slots and return
        if not confirm_cancellation:
            dispatcher.utter_message(response="utter_cancellation_aborted")
            return [
                SlotSet("appointment_id", None),
                SlotSet("phone_number", None),
                SlotSet("confirm_cancellation", None),
                FollowupAction("action_listen")
            ]

        # If we don't have both appointment_id and phone_number, we can't proceed
        if not appointment_id or not phone_number:
            dispatcher.utter_message(text="I need both your appointment ID and phone number to cancel your appointment.")
            return [
                SlotSet("appointment_id", None),
                SlotSet("phone_number", None),
                SlotSet("confirm_cancellation", None),
                FollowupAction("action_listen")
            ]

        # Database operations to cancel the appointment
        conn = None
        success = False
        cancelled_appointment_details = None

        try:
            conn = get_db_connection()
            if not conn:
                logger.error("Failed to connect to the database for appointment cancellation.")
                dispatcher.utter_message(response="utter_cancellation_failed")
                return [
                    SlotSet("appointment_id", None),
                    SlotSet("phone_number", None),
                    SlotSet("confirm_cancellation", None),
                    FollowupAction("action_listen")
                ]

            cur = conn.cursor()

            # 1. Get appointment details for confirmation message
            logger.debug(f"Fetching appointment details for ID: {appointment_id} and phone: {phone_number}")

            # Now execute the query to get appointment details
            cur.execute(
                """
                SELECT
                    a.appointment_id,
                    a.service_type,
                    a.appointment_date,
                    a.appointment_time,
                    d.name as doctor_name
                FROM appointments a
                JOIN users u ON a.user_id = u.user_id
                JOIN doctors d ON a.doctor_id = d.doctor_id
                WHERE a.appointment_id = %s AND u.phone = %s AND a.status = 'booked'
                """,
                (appointment_id, phone_number)
            )

            appointment = cur.fetchone()

            if not appointment:
                dispatcher.utter_message(text="I couldn't find an active appointment with the provided details.")
                return [
                    SlotSet("appointment_id", None),
                    SlotSet("phone_number", None),
                    SlotSet("confirm_cancellation", None),
                    FollowupAction("action_listen")
                ]

            # Store details for confirmation message
            appointment_id = appointment['appointment_id']
            service_type = appointment['service_type']
            appointment_date = appointment['appointment_date']
            appointment_time = appointment['appointment_time']
            doctor_name = appointment['doctor_name']

            logger.debug(f"Retrieved appointment details: ID={appointment_id}, service={service_type}, date={appointment_date}, time={appointment_time}, doctor={doctor_name}")

            # 2. Update appointment status to 'cancelled'
            logger.debug(f"Updating appointment status to 'cancelled' for ID: {appointment_id}")
            cur.execute(
                "UPDATE appointments SET status = 'cancelled' WHERE appointment_id = %s",
                (appointment_id,)
            )
            logger.debug(f"Rows affected by appointment update: {cur.rowcount}")

            # 3. Update the doctor_availability table to free up the slot
            logger.debug(f"Freeing up availability slot for appointment ID: {appointment_id}")
            cur.execute(
                """
                UPDATE doctor_availability
                SET is_booked = FALSE, booked_by_appointment_id = NULL
                WHERE booked_by_appointment_id = %s
                """,
                (appointment_id,)
            )
            logger.debug(f"Rows affected by availability update: {cur.rowcount}")

            # Commit the transaction
            conn.commit()
            success = True

            # Format details for confirmation message
            appointment_datetime = datetime.combine(appointment_date, appointment_time)
            formatted_datetime = appointment_datetime.strftime("%A, %B %d at %I:%M %p")

            cancelled_appointment_details = {
                "service_type": service_type,
                "datetime": formatted_datetime,
                "doctor_name": doctor_name
            }

            logger.debug(f"Successfully cancelled appointment ID: {appointment_id}")

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            logger.error(f"Database error during appointment cancellation: {e}")
            logger.error(f"Error traceback: {error_traceback}")

            if conn:
                conn.rollback()

            # Try to get more information about the database schema
            try:
                if conn:
                    cur = conn.cursor()
                    # Check appointments table
                    cur.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'appointments'")
                    appointment_columns = [row[0] for row in cur.fetchall()]
                    logger.debug(f"Appointments table columns from schema: {appointment_columns}")

                    # Check doctor_availability table
                    cur.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'doctor_availability'")
                    availability_columns = [row[0] for row in cur.fetchall()]
                    logger.debug(f"Doctor_availability table columns from schema: {availability_columns}")
            except Exception as schema_error:
                logger.error(f"Error getting schema information: {schema_error}")

            dispatcher.utter_message(response="utter_cancellation_failed")
            return [
                SlotSet("appointment_id", None),
                SlotSet("phone_number", None),
                SlotSet("confirm_cancellation", None),
                FollowupAction("action_listen")
            ]
        finally:
            if conn:
                conn.close()

        # Send confirmation if cancellation was successful
        if success and cancelled_appointment_details:
            dispatcher.utter_message(
                text=f"I've cancelled your {cancelled_appointment_details['service_type']} appointment "
                     f"on {cancelled_appointment_details['datetime']} with Dr. {cancelled_appointment_details['doctor_name']}. "
                     f"Is there anything else I can help you with?"
            )
            dispatcher.utter_message(response="utter_cancellation_success")
        elif not success:
            dispatcher.utter_message(response="utter_cancellation_failed")

        # Clear slots
        return [
            SlotSet("appointment_id", None),
            SlotSet("phone_number", None),
            SlotSet("confirm_cancellation", None),
            FollowupAction("action_listen")
        ]
