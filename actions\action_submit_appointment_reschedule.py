"""
Action to submit appointment reschedule after form completion.
This file handles the actual rescheduling of the appointment.
For demo purposes, it works without database integration but is structured
to easily add database operations later.
"""

from typing import Any, Text, Dict, List
import logging
from datetime import datetime

from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet, FollowupAction

logger = logging.getLogger(__name__)

# Mock appointment database for demo purposes
# TODO: Replace with actual database queries when integrating with database
MOCK_APPOINTMENTS = {
    "1001": {
        "appointment_id": "1001",
        "service_type": "Physical Therapy & Comprehensive Pain Management",
        "appointment_date": "Monday, May 20, 2024",
        "appointment_time": "10:00 AM",
        "doctor_name": "Dr. <PERSON>",
        "phone_number": "9123456789",
        "status": "booked"
    },
    "1002": {
        "appointment_id": "1002",
        "service_type": "Weight Loss Programs",
        "appointment_date": "Wednesday, May 22, 2024",
        "appointment_time": "2:00 PM",
        "doctor_name": "Dr. <PERSON>",
        "phone_number": "9876543210",
        "status": "booked"
    },
    "1003": {
        "appointment_id": "1003",
        "service_type": "Neurological Rehabilitation",
        "appointment_date": "Friday, May 24, 2024",
        "appointment_time": "4:00 PM",
        "doctor_name": "Dr. Emily Rodriguez",
        "phone_number": "9555666777",
        "status": "booked"
    },
    "1004": {
        "appointment_id": "1004",
        "service_type": "Orthopedic Rehabilitation",
        "appointment_date": "Tuesday, May 21, 2024",
        "appointment_time": "11:30 AM",
        "doctor_name": "Dr. James Wilson",
        "phone_number": "9444555666",
        "status": "booked"
    },
    "1005": {
        "appointment_id": "1005",
        "service_type": "Gynaecology Rehabilitation & Prenatal Care",
        "appointment_date": "Thursday, May 23, 2024",
        "appointment_time": "3:30 PM",
        "doctor_name": "Dr. Lisa Patel",
        "phone_number": "9333444555",
        "status": "booked"
    }
}

class ActionSubmitAppointmentReschedule(Action):
    """
    Action to handle the actual rescheduling of an appointment after form completion.

    This action is called after the appointment_reschedule_form is complete
    and the user has confirmed the reschedule. It updates the appointment
    in the system (currently mock, but structured for database integration).
    """

    def name(self) -> Text:
        return "action_submit_appointment_reschedule"

    def run(
        self,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: Dict[Text, Any]
    ) -> List[Dict[Text, Any]]:
        """
        Reschedule the appointment and provide confirmation.
        """
        # Get all the required information from slots
        appointment_id = tracker.get_slot("reschedule_appointment_id")
        phone_number = tracker.get_slot("reschedule_phone_number")
        new_datetime = tracker.get_slot("new_appointment_datetime")
        confirm_reschedule = tracker.get_slot("confirm_reschedule")

        logger.debug(f"ActionSubmitAppointmentReschedule: appointment_id={appointment_id}, "
                    f"phone_number={phone_number}, new_datetime={new_datetime}, "
                    f"confirm_reschedule={confirm_reschedule}")

        # If the user didn't confirm reschedule, just clear slots and return
        if not confirm_reschedule:
            dispatcher.utter_message(response="utter_reschedule_cancelled")
            return self._clear_reschedule_slots()

        # If we don't have all required information, we can't proceed
        if not all([appointment_id, phone_number, new_datetime]):
            dispatcher.utter_message(text="I need your appointment ID, phone number, and new date/time to reschedule your appointment.")
            return self._clear_reschedule_slots()

        # Get original appointment details for confirmation message
        original_appointment = "Monday, May 20, 2024 at 10:00 AM"
        service_type = "Physical Therapy & Comprehensive Pain Management"
        doctor_name = "Dr. Sarah Johnson"

        if appointment_id in MOCK_APPOINTMENTS:
            appointment = MOCK_APPOINTMENTS[appointment_id]
            original_appointment = f"{appointment['appointment_date']} at {appointment['appointment_time']}"
            service_type = appointment['service_type']
            doctor_name = appointment['doctor_name']

        # TODO: Database operations will go here when integrating with database
        # The following operations should be performed in a transaction:
        # 1. Verify the original appointment exists and belongs to the user
        # 2. Check if the new time slot is available
        # 3. Free up the old time slot in doctor_availability table
        # 4. Book the new time slot in doctor_availability table
        # 5. Update the appointment record with new date and time
        
        # For now, we simulate successful reschedule
        success = True
        
        if success:
            # Success message with dynamic date/time
            dispatcher.utter_message(
                text=f"🎉 **Perfect! Your appointment has been successfully rescheduled!**\n\n"
                     f"📅 **Appointment ID:** {appointment_id}\n"
                     f"🏥 **Service:** {service_type}\n"
                     f"👨‍⚕️ **Doctor:** {doctor_name}\n"
                     f"📆 **New Date & Time:** {new_datetime}\n"
                     f"📱 **Confirmation sent to:** {phone_number}\n\n"
                     f"✅ **Changed from:** {original_appointment}\n"
                     f"✅ **Changed to:** {new_datetime}\n\n"
                     f"We look forward to seeing you at your new appointment time! "
                     f"You'll receive a confirmation message shortly."
            )
        else:
            # This would handle database errors in the real implementation
            dispatcher.utter_message(text="I'm sorry, there was an issue rescheduling your appointment. Please try again or contact our office directly.")

        # Clear all reschedule slots
        return self._clear_reschedule_slots()

    def _clear_reschedule_slots(self) -> List[Dict[Text, Any]]:
        """Helper method to clear all reschedule-related slots."""
        return [
            SlotSet("reschedule_appointment_id", None),
            SlotSet("reschedule_phone_number", None),
            SlotSet("new_appointment_datetime", None),
            SlotSet("confirm_reschedule", None),
            SlotSet("new_stored_date", None),
            SlotSet("new_stored_time", None),
            SlotSet("mock_service_type", None),
            SlotSet("mock_appointment_datetime", None),
            SlotSet("mock_doctor_name", None),
            FollowupAction("action_listen")
        ]
