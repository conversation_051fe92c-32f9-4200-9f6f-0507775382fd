# This files contains your custom actions which can be used to run
# custom Python code.
#
# See this guide on how to implement these action:
# https://rasa.com/docs/rasa/custom-actions

# --- IMPORTANT: Appointment Verification Component ---
# This component allows users to verify their appointments by providing:
# 1. Appointment ID
# 2. Phone number used during booking
#
# The component will:
# - Validate both pieces of information
# - Check if they match in the database
# - Return appointment details if verification is successful
# - Provide appropriate error messages if verification fails

from typing import Any, Text, Dict, List
import re # Import regex module for validation
from datetime import datetime, timedelta, date
import dateparser  # For intelligent date parsing
import logging # Import logging
import os
import psycopg2
from psycopg2 import extras

from rasa_sdk import Action, Tracker, FormValidationAction
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.types import DomainDict
from rasa_sdk.events import SlotSet, EventType, FollowupAction, ActiveLoop # Added FollowupAction and ActiveLoop

# Import our custom components
from actions.action_extract_slots import ActionExtractAppointmentVerificationSlots

# Import reschedule-related actions
from actions.action_extract_reschedule_slots import ActionExtractAppointmentRescheduleSlots
from actions.validate_appointment_reschedule_form import ValidateAppointmentRescheduleForm
from actions.action_show_current_appointment import ActionShowCurrentAppointment
from actions.action_submit_appointment_reschedule import ActionSubmitAppointmentReschedule

logger = logging.getLogger(__name__) # Setup logger

# Import database connection function from db_utils.py
from actions.db_utils import get_db_connection

# Define the list of valid services offered by the clinic
VALID_SERVICES = [
    "Physical Therapy & Comprehensive Pain Management",
    "Weight Loss Programs",
    "Neurological Rehabilitation",
    "Orthopedic Rehabilitation",
    "Gynaecology Rehabilitation & Prenatal Care"
]

# Create a mapping dictionary for service name variations to their standardized names
SERVICE_VARIATIONS = {
    # Physical Therapy & Comprehensive Pain Management variations
    "physical therapy": "Physical Therapy & Comprehensive Pain Management",
    "physical therapy & comprehensive pain management": "Physical Therapy & Comprehensive Pain Management",
    "comprehensive pain management": "Physical Therapy & Comprehensive Pain Management",
    "pain management": "Physical Therapy & Comprehensive Pain Management",
    "physio therapy": "Physical Therapy & Comprehensive Pain Management",
    "pain managment": "Physical Therapy & Comprehensive Pain Management",
    "pt": "Physical Therapy & Comprehensive Pain Management",
    "physical therpay": "Physical Therapy & Comprehensive Pain Management",
    "pain mngmnt": "Physical Therapy & Comprehensive Pain Management",
    "physiotherapy": "Physical Therapy & Comprehensive Pain Management",
    "physio": "Physical Therapy & Comprehensive Pain Management",

    # Weight Loss Programs variations
    "weight loss": "Weight Loss Programs",
    "weight loss programs": "Weight Loss Programs",
    "weightloss": "Weight Loss Programs",
    "weight lose": "Weight Loss Programs",
    "weightlos": "Weight Loss Programs",
    "weight loss programms": "Weight Loss Programs",
    "weight lossing": "Weight Loss Programs",
    "weight lose program": "Weight Loss Programs",
    "weightlose": "Weight Loss Programs",
    "weightlos program": "Weight Loss Programs",
    "weight loss program": "Weight Loss Programs",

    # Neurological Rehabilitation variations
    "neurological rehabilitation": "Neurological Rehabilitation",
    "neurological rehab": "Neurological Rehabilitation",
    "neuro rehabilitation": "Neurological Rehabilitation",
    "neuro rehab": "Neurological Rehabilitation",
    "neurological reabilitation": "Neurological Rehabilitation",
    "neuro rehab appointment": "Neurological Rehabilitation",

    # Orthopedic Rehabilitation variations
    "orthopedic rehabilitation": "Orthopedic Rehabilitation",
    "orthopedic rehab": "Orthopedic Rehabilitation",
    "ortho rehabilitation": "Orthopedic Rehabilitation",
    "ortho rehab": "Orthopedic Rehabilitation",
    "orthopaedic rehabilitation": "Orthopedic Rehabilitation",
    "ortho rehab appointment": "Orthopedic Rehabilitation",
    "orthopedic": "Orthopedic Rehabilitation",

    # Gynaecology Rehabilitation & Prenatal Care variations
    "gynaecology rehabilitation & prenatal care": "Gynaecology Rehabilitation & Prenatal Care",
    "gynaecology rehabilitation": "Gynaecology Rehabilitation & Prenatal Care",
    "prenatal care": "Gynaecology Rehabilitation & Prenatal Care",
    "gynaecology & prenatal care": "Gynaecology Rehabilitation & Prenatal Care",
    "gynaecology rehab": "Gynaecology Rehabilitation & Prenatal Care",
    "prenatal": "Gynaecology Rehabilitation & Prenatal Care",
    "gyno rehab": "Gynaecology Rehabilitation & Prenatal Care",
    "gynecology rehabilitation": "Gynaecology Rehabilitation & Prenatal Care",
    "gynaecology and prenatal": "Gynaecology Rehabilitation & Prenatal Care",
    "gyno": "Gynaecology Rehabilitation & Prenatal Care",
    "prenatal rehab": "Gynaecology Rehabilitation & Prenatal Care",
}

# Define descriptions for each service (from pre-written logic)
SERVICE_DESCRIPTIONS = {
    "Physical Therapy & Comprehensive Pain Management": """
Physical Therapy & Comprehensive Pain Management at Arham Centre focuses on treating pain and improving mobility.
Our services include:
- Advanced manual therapy techniques
- Personalized exercise programs
- Electrotherapy and ultrasound therapy
- Trigger point therapy and myofascial release
- Joint mobilization and manipulation
- Postural assessment and correction
This service is ideal for patients recovering from injuries, surgeries, or dealing with chronic pain conditions. Our therapists work closely with you to develop a customized treatment plan targeting your specific pain points and mobility issues.
Treatment sessions typically last 45-60 minutes, and many patients experience improvement within 3-6 sessions.
""",

    "Weight Loss Programs": """
Our Weight Loss Programs at Arham Centre combine physiotherapy principles with nutrition guidance and exercise science.
Program components include:
- Comprehensive body composition analysis
- Personalized exercise prescription
- Nutritional counseling and meal planning assistance
- Regular progress tracking and adjustments
- Therapeutic exercises for optimal fat loss
- Education on sustainable lifestyle changes
Our physiotherapists specialize in weight management issues related to medical conditions and mobility limitations. We address weight concerns while considering joint health, metabolic factors, and any existing medical conditions.
Programs typically run for 8-12 weeks with weekly or bi-weekly sessions, and include ongoing support to maintain your results long-term.
""",

    "Neurological Rehabilitation": """
Neurological Rehabilitation at Arham Centre is tailored for individuals with conditions affecting the nervous system.

Our approach includes:
- Specialized neuro-physiotherapy techniques
- Functional electrical stimulation
- Balance and coordination training
- Cognitive-motor dual-task training
- Gait retraining and mobility assistance
- Activities of daily living (ADL) training

This service helps patients with stroke recovery, multiple sclerosis, Parkinson's disease, traumatic brain injuries, and other neurological conditions. We focus on restoring function, improving independence, and enhancing quality of life.

Treatment plans are highly individualized, with regular assessments to track progress and adjust therapy accordingly.
""",

    "Orthopedic Rehabilitation": """
Orthopedic Rehabilitation at Arham Centre specializes in treating musculoskeletal issues and post-surgical recovery.

Our comprehensive services include:
- Joint-specific rehabilitation protocols
- Post-surgical rehabilitation
- Sports injury recovery programs
- Manual therapy and joint mobilization
- Strength and flexibility training
- Biomechanical analysis and correction

This service addresses conditions like joint replacements, ligament/tendon repairs, fractures, arthritis, sports injuries, and chronic musculoskeletal pain. We work closely with orthopedic surgeons to ensure continuity of care.

Treatment duration varies based on condition severity, typically ranging from 4-12 weeks with sessions 2-3 times weekly during active rehabilitation.
""",

    "Gynaecology Rehabilitation & Prenatal Care": """
Our Gynaecology Rehabilitation & Prenatal Care services at Arham Centre provide specialized physiotherapy for women's health concerns.

Services include:
- Prenatal and postnatal physiotherapy
- Pelvic floor assessment and rehabilitation
- Pregnancy-related pain management
- Postpartum recovery programs
- Diastasis recti treatment
- Preparation for labor and delivery

These services support women through pregnancy, childbirth recovery, and gynaecological issues such as incontinence or pelvic pain. Our female physiotherapists create comfortable, private environments for these sensitive treatments.

Programs typically include an initial assessment and regular sessions tailored to your stage of pregnancy or recovery, with home exercise programs to complement in-clinic therapy.
"""
}


# Action to handle booking with database integration
class ActionBookAppointment(Action):
    def name(self) -> Text:
        return "action_book_appointment"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        # Rest of original booking logic remains unchanged
        service_type = tracker.get_slot("service_type")
        appointment_datetime_str = tracker.get_slot("appointment_datetime")
        name = tracker.get_slot("name")
        phone_number = tracker.get_slot("phone_number")

        logger.debug(f"Booking appointment with: service={service_type}, datetime={appointment_datetime_str}, name={name}, phone={phone_number}")

        # Extract all slots
        service_type = tracker.get_slot("service_type")
        appointment_datetime_str = tracker.get_slot("appointment_datetime")
        name = tracker.get_slot("name")
        phone_number = tracker.get_slot("phone_number")

        logger.debug(f"Booking appointment with: service={service_type}, datetime={appointment_datetime_str}, name={name}, phone={phone_number}")

        # Check if we have all required slot values
        if not all([service_type, appointment_datetime_str, name, phone_number]):
            dispatcher.utter_message(text="Sorry, I don't have all the information needed to book your appointment.")
            return [SlotSet("service_type", None),
                    SlotSet("appointment_datetime", None),
                    SlotSet("name", None),
                    SlotSet("phone_number", None),
                    SlotSet("stored_date", None),
                    SlotSet("stored_time", None)]

        # Parse the appointment_datetime string into separate date and time components
        # Expected format: "Monday, May 20 at 03:00 PM"
        try:
            # Check if we have a stored_date from a previous availability check
            stored_date_str = tracker.get_slot("stored_date")
            logger.debug(f"Found stored_date: {stored_date_str}")

            # Parse the datetime string to a Python datetime object
            parsed_datetime = dateparser.parse(appointment_datetime_str)
            if not parsed_datetime:
                raise ValueError(f"Could not parse datetime: {appointment_datetime_str}")

            # Extract date and time components
            appointment_time = parsed_datetime.time()

            # If we have a stored_date from availability check, use it instead of the parsed date
            if stored_date_str:
                try:
                    # Try to parse the stored date (which should be in YYYY-MM-DD format)
                    if re.match(r'^\d{4}-\d{2}-\d{2}$', stored_date_str):
                        year, month, day = map(int, stored_date_str.split('-'))
                        stored_date = date(year, month, day)
                        appointment_date = stored_date
                        logger.debug(f"Using stored_date for appointment: {appointment_date}")
                    else:
                        # If not in expected format, try with dateparser
                        stored_date_parsed = dateparser.parse(stored_date_str)
                        if stored_date_parsed:
                            appointment_date = stored_date_parsed.date()
                            logger.debug(f"Using parsed stored_date for appointment: {appointment_date}")
                        else:
                            # Fall back to the parsed datetime's date
                            appointment_date = parsed_datetime.date()
                            logger.debug(f"Using parsed datetime's date for appointment: {appointment_date}")
                except Exception as date_error:
                    logger.error(f"Error parsing stored_date '{stored_date_str}': {date_error}")
                    # Fall back to the parsed datetime's date
                    appointment_date = parsed_datetime.date()
                    logger.debug(f"Falling back to parsed datetime's date: {appointment_date}")
            else:
                # No stored_date, use the parsed datetime's date
                appointment_date = parsed_datetime.date()
                logger.debug(f"No stored_date, using parsed datetime's date: {appointment_date}")

            # Add debug logging to verify the correct date is being used
            logger.debug(f"Final decision - Booking appointment for date={appointment_date}, time={appointment_time}")
        except Exception as e:
            logger.error(f"Error parsing appointment datetime: {e}")
            dispatcher.utter_message(text="Sorry, there was an issue processing your appointment time. Please try again.")
            return [SlotSet("appointment_datetime", None)]

        # Database operations
        conn = None
        success = False
        booked_appointment_details = None

        try:
            # Get database connection
            conn = get_db_connection()
            if not conn:
                raise Exception("Failed to connect to the database")

            # Create a cursor for operations
            cur = conn.cursor()

            # 1. Find or Create User
            # Check if user exists with this phone number
            cur.execute("SELECT user_id FROM users WHERE phone = %s", (phone_number,))
            user_result = cur.fetchone()

            if user_result:
                # User exists, get their ID
                user_id = user_result['user_id']
                logger.debug(f"Found existing user with ID: {user_id}")
            else:
                # Create new user
                cur.execute(
                    "INSERT INTO users (name, phone) VALUES (%s, %s) RETURNING user_id",
                    (name, phone_number)
                )
                user_id = cur.fetchone()['user_id']
                logger.debug(f"Created new user with ID: {user_id}")

            # 2. Find Available Slot
            # Find an available slot for the requested time
            # Log the exact time we're looking for
            logger.debug(f"Looking for slot on {appointment_date} at {appointment_time}")

            # First try to find an exact match by start_time
            cur.execute(
                """
                SELECT availability_id, doctor_id, start_time
                FROM doctor_availability
                WHERE DATE(start_time) = %s
                AND EXTRACT(HOUR FROM start_time) = %s
                AND EXTRACT(MINUTE FROM start_time) = %s
                AND is_booked = FALSE
                LIMIT 1
                """,
                (appointment_date, appointment_time.hour, appointment_time.minute)
            )

            slot_result = cur.fetchone()

            if not slot_result:
                # No available slot found
                logger.debug(f"No available slots for {appointment_date} at {appointment_time}")

                # Find alternative slots to suggest
                cur.execute(
                    """
                    SELECT start_time
                    FROM doctor_availability
                    WHERE DATE(start_time) = %s
                    AND is_booked = FALSE
                    ORDER BY start_time
                    LIMIT 3
                    """,
                    (appointment_date,)
                )

                alternative_slots = cur.fetchall()

                # Suggest alternative times
                if alternative_slots:
                    alt_times = [slot['start_time'].strftime("%I:%M %p") for slot in alternative_slots]
                    dispatcher.utter_message(
                        text=f"Sorry, we don't have availability at {appointment_time.strftime('%I:%M %p')} on {appointment_date}. "
                             f"We do have openings at: {', '.join(alt_times)}. Would you like to book one of these times instead?"
                    )
                else:
                    dispatcher.utter_message(
                        text=f"Sorry, we don't have any availability on {appointment_date}. "
                             f"Please try a different date."
                    )

                # Keep the service_type but clear datetime to let user select a new time
                return [SlotSet("appointment_datetime", None),
                        SlotSet("stored_date", None),
                        SlotSet("stored_time", None)]

            # We found an available slot
            availability_id = slot_result['availability_id']
            doctor_id = slot_result['doctor_id']

            # 3. Create Appointment Record
            cur.execute(
                """
                INSERT INTO appointments
                (user_id, doctor_id, service_type, appointment_date, appointment_time, status)
                VALUES (%s, %s, %s, %s, %s, 'booked')
                RETURNING appointment_id
                """,
                (user_id, doctor_id, service_type, appointment_date, appointment_time, )
            )

            appointment_id = cur.fetchone()['appointment_id']

            # 4. Update Availability Record
            cur.execute(
                """
                UPDATE doctor_availability
                SET is_booked = TRUE, booked_by_appointment_id = %s
                WHERE availability_id = %s
                """,
                (appointment_id, availability_id)
            )

            # Get doctor's name for confirmation message
            cur.execute("SELECT name FROM doctors WHERE doctor_id = %s", (doctor_id,))
            doctor_name = cur.fetchone()['name']

            # Commit the transaction
            conn.commit()
            success = True

            # Store details for confirmation message
            booked_appointment_details = {
                "doctor_name": doctor_name,
                "appointment_id": appointment_id
            }

            logger.debug(f"Successfully booked appointment ID: {appointment_id} with doctor: {doctor_name}")

        except Exception as e:
            logger.error(f"Database error during appointment booking: {e}")
            if conn:
                conn.rollback()
            dispatcher.utter_message(text="Sorry, we encountered an error while booking your appointment. Please try again later.")
            return []
        finally:
            if conn:
                conn.close()

        # Send confirmation if booking was successful
        if success and booked_appointment_details:
            doctor_name = booked_appointment_details["doctor_name"]
            appointment_id = booked_appointment_details["appointment_id"]

            dispatcher.utter_message(
                text=f"Awesome, {name}! 🎉 Your {service_type} appointment is all booked for {appointment_datetime_str} "
                     f"with {doctor_name}.Your Appointment ID is {appointment_id}; please use this number for any follow-up questions."
                     f"We're also sending a confirmation text to {phone_number} right now! See you there!"
            )
        elif not success:
            dispatcher.utter_message(text="Sorry, there was an issue booking your appointment. Please try again.")

        # Clear the slots for next booking
        return [SlotSet("service_type", None),
                SlotSet("appointment_datetime", None),
                SlotSet("name", None),
                SlotSet("phone_number", None),
                SlotSet("stored_date", None),
                SlotSet("stored_time", None),
                SlotSet("confirming_name", None),
                SlotSet("name_candidate", None)]


# Action to handle cancellation with database integration
class ActionCancelAppointment(Action):
    def name(self) -> Text:
        return "action_cancel_appointment"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        # Extract identifying information
        # Note: For a complete implementation, you would need additional dialogue steps
        # to identify which specific appointment to cancel if a user has multiple
        phone_number = tracker.get_slot("phone_number")

        if not phone_number:
            dispatcher.utter_message(text="I need your phone number to find your appointment. What's your phone number?")
            return []

        logger.debug(f"Attempting to cancel appointment for phone: {phone_number}")

        # Database operations
        conn = None
        success = False
        cancelled_appointment_details = None

        try:
            # Get database connection
            conn = get_db_connection()
            if not conn:
                raise Exception("Failed to connect to the database")

            cur = conn.cursor()

            # 1. Find the user by phone number
            cur.execute("SELECT user_id FROM users WHERE phone = %s", (phone_number,))
            user_result = cur.fetchone()

            if not user_result:
                dispatcher.utter_message(text="I couldn't find any appointments associated with your phone number.")
                return []

            user_id = user_result['user_id']

            # 2. Find upcoming appointments for this user
            cur.execute(
                """
                SELECT
                    a.appointment_id,
                    a.service_type,
                    a.appointment_date,
                    a.appointment_time,
                    d.name as doctor_name,
                    a.booked_by_appointment_id
                FROM appointments a
                JOIN doctors d ON a.doctor_id = d.doctor_id
                WHERE a.user_id = %s
                AND a.status = 'booked'
                AND a.appointment_date >= CURRENT_DATE
                ORDER BY a.appointment_date, a.appointment_time
                LIMIT 1
                """,
                (user_id,)
            )

            appointment = cur.fetchone()

            if not appointment:
                dispatcher.utter_message(text="You don't have any upcoming appointments to cancel.")
                return []

            # Store details for confirmation message
            appointment_id = appointment['appointment_id']
            service_type = appointment['service_type']
            appointment_date = appointment['appointment_date']
            appointment_time = appointment['appointment_time']
            doctor_name = appointment['doctor_name']
            availability_id = appointment['booked_by_appointment_id']

            # 3. Update appointment status to 'cancelled'
            cur.execute(
                "UPDATE appointments SET status = 'cancelled' WHERE appointment_id = %s",
                (appointment_id,)
            )

            # 4. If the appointment is linked to an availability slot, free it up
            if availability_id:
                cur.execute(
                    """
                    UPDATE doctor_availability
                    SET is_booked = FALSE, booked_by_appointment_id = NULL
                    WHERE booked_by_appointment_id = %s
                    """,
                    (appointment_id,)
                )

            # Commit the transaction
            conn.commit()
            success = True

            # Format details for confirmation message
            appointment_datetime = datetime.combine(appointment_date, appointment_time)
            formatted_datetime = appointment_datetime.strftime("%A, %B %d at %I:%M %p")

            cancelled_appointment_details = {
                "service_type": service_type,
                "datetime": formatted_datetime,
                "doctor_name": doctor_name
            }

            logger.debug(f"Successfully cancelled appointment ID: {appointment_id}")

        except Exception as e:
            logger.error(f"Database error during appointment cancellation: {e}")
            if conn:
                conn.rollback()
            dispatcher.utter_message(text="Sorry, we encountered an error while cancelling your appointment. Please try again later.")
            return []
        finally:
            if conn:
                conn.close()

        # Send confirmation if cancellation was successful
        if success and cancelled_appointment_details:
            dispatcher.utter_message(
                text=f"I've cancelled your {cancelled_appointment_details['service_type']} appointment "
                     f"on {cancelled_appointment_details['datetime']} with Dr. {cancelled_appointment_details['doctor_name']}. "
                     f"Is there anything else I can help you with?"
            )
        elif not success:
            dispatcher.utter_message(text="Sorry, there was an issue cancelling your appointment. Please try again or contact our office directly.")

        # Clear slots
        return [SlotSet("service_type", None),
                SlotSet("appointment_datetime", None),
                SlotSet("name", None),
                SlotSet("phone_number", None),
                SlotSet("stored_date", None),
                SlotSet("stored_time", None)]

# New custom action to provide service details (as discussed in Step 178)
# Action to get appointment details after verification
class ActionGetAppointmentDetails(Action):
    def name(self) -> Text:
        return "action_get_appointment_details"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        """Get appointment details after successful verification."""

        appointment_id = tracker.get_slot("appointment_id")
        phone_number = tracker.get_slot("phone_number")

        logger.debug(f"Getting appointment details for ID={appointment_id}, phone={phone_number}")

        if not appointment_id or not phone_number:
            dispatcher.utter_message(text="I need both your appointment ID and phone number to retrieve your appointment details.")
            return [
                SlotSet("appointment_id", None),
                SlotSet("phone_number", None),
                FollowupAction("action_listen")
            ]

        conn = None
        try:
            conn = get_db_connection()
            if not conn:
                logger.error("Failed to connect to the database for appointment details retrieval.")
                dispatcher.utter_message(text="I'm having trouble retrieving your appointment details. Please try again later.")
                return [
                    SlotSet("appointment_id", None),
                    SlotSet("phone_number", None),
                    FollowupAction("action_listen")
                ]

            cur = conn.cursor()
            cur.execute(
                """
                SELECT
                    a.appointment_id,
                    a.service_type,
                    a.appointment_date,
                    a.appointment_time,
                    d.name as doctor_name,
                    u.name as patient_name
                FROM appointments a
                JOIN users u ON a.user_id = u.user_id
                JOIN doctors d ON a.doctor_id = d.doctor_id
                WHERE a.appointment_id = %s AND u.phone = %s AND a.status = 'booked'
                """,
                (appointment_id, phone_number)
            )

            appointment = cur.fetchone()

            if not appointment:
                dispatcher.utter_message(response="utter_no_appointment_found")
                return [
                    SlotSet("appointment_id", None),
                    SlotSet("phone_number", None),
                    FollowupAction("action_listen")
                ]

            # Format the appointment details for display
            appointment_date = appointment['appointment_date']
            appointment_time = appointment['appointment_time']
            formatted_date = appointment_date.strftime("%A, %B %d, %Y")
            formatted_time = appointment_time.strftime("%I:%M %p").lstrip("0")

            service_type = appointment['service_type']
            doctor_name = appointment['doctor_name']
            patient_name = appointment['patient_name']

            # Send the appointment details to the user
            dispatcher.utter_message(
                text=f"Here are your appointment details, {patient_name}:\n\n"
                     f"Appointment ID: {appointment_id}\n"
                     f"Service: {service_type}\n"
                     f"Date: {formatted_date}\n"
                     f"Time: {formatted_time}\n"
                     f"Doctor: {doctor_name}\n\n"
                     f"Is there anything else you'd like to know about your appointment?"
            )

            # Log that we're explicitly deactivating the form
            logger.info("ActionGetAppointmentDetails: Explicitly deactivating form and clearing slots")

            # Add a clear message that verification is complete
            dispatcher.utter_message(text="The appointment verification is now complete. You can ask me anything else.")

            # Clear verification slots and add action_listen to wait for user input after showing details
            return [
                SlotSet("appointment_id", None),
                SlotSet("phone_number", None),
                SlotSet("requested_slot", None),
                ActiveLoop(None),  # Explicitly deactivate any active form
                FollowupAction("action_listen")  # Force the bot to listen for new input
            ]

        except Exception as e:
            logger.error(f"Database error during appointment details retrieval: {e}")
            dispatcher.utter_message(text="I'm having trouble retrieving your appointment details. Please try again later.")
            return [
                SlotSet("appointment_id", None),
                SlotSet("phone_number", None),
                FollowupAction("action_listen")
            ]
        finally:
            if conn:
                conn.close()

class ActionProvideServiceDetails(Action):
    def name(self) -> Text:
        return "action_provide_service_details"

    def run(
        self,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> List[Dict[Text, Any]]:
        """Provides details about a specific service based on the 'service_type' slot."""

        # Get the service type from the latest entity or the slot
        service_type = None

        # First try to get from the last user message's entities
        for entity in tracker.latest_message.get('entities', []):
            if entity['entity'] == 'service_type':
                service_type = entity['value']
                logger.debug(f"Extracted service_type entity '{service_type}' from latest message.")
                break # Found the entity, stop searching

        # If not found in the latest message, try the service_type slot value
        # (This might be from a previous turn or validation)
        if not service_type:
            service_type = tracker.get_slot("service_type")
            logger.debug(f"Got service_type from slot: {service_type}")

        # If we have a service type, provide details
        if not service_type:
            message_text = tracker.latest_message.get('text', '').lower()

            # Check for keywords in the message
            for service_variation, official_name in SERVICE_VARIATIONS.items():
                if service_variation in message_text:
                    service_type = official_name
                    logger.debug(f"Found service keyword '{service_variation}' in message.")
                    break

        # If still not found, try the service_type slot value
        if not service_type:
            service_type = tracker.get_slot("service_type")
            logger.debug(f"Got service_type from slot: {service_type}")

        # If we have a service type, provide details
        if service_type:
            # Normalize service type
            normalized_service = SERVICE_VARIATIONS.get(str(service_type).lower().strip())

            # If not found in variations, try substring matching
            if not normalized_service:
                for valid_service in VALID_SERVICES:
                    if str(service_type).lower().strip() in valid_service.lower():
                        normalized_service = valid_service
                        break

            # If we found a match
            if normalized_service:
                description = SERVICE_DESCRIPTIONS.get(normalized_service)
                if description:
                    logger.debug(f"Found description for service: {normalized_service}")
                    dispatcher.utter_message(text=f"Here's detailed information about {normalized_service}:")
                    dispatcher.utter_message(text=description)

                    # Ask if they want to book this service - helps guide the conversation
                    dispatcher.utter_message(text=f"Would you like to book an appointment for {normalized_service}?")
                else:
                    logger.error(f"ERROR: No description found for service: {normalized_service}")
                    dispatcher.utter_message(text=f"Sorry, I don't have detailed information about '{normalized_service}' at the moment.")
            else:
                dispatcher.utter_message(text=f"I don't have specific details about '{service_type}'. Would you like to know about our other services?")
        else:
            dispatcher.utter_message(text="Which service would you like to learn more about? We offer Physical Therapy & Pain Management, Weight Loss Programs, Neurological Rehabilitation, Orthopedic Rehabilitation, and Gynaecology Rehabilitation & Prenatal Care.")

        # Return an empty list of events
        return []

# Form validation action (using structure from pre-written, methods from current)
class ValidateAppointmentForm(FormValidationAction):
    def name(self) -> Text:
        return "validate_appointment_form"

    def validate_service_type(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> Dict[Text, Any]:
        """Validate service_type value."""
        logger.debug(f"Validating service_type = {slot_value}")

        # Check for interruptions - if user is asking for information instead of providing it
        # Extract intent from latest message
        latest_intent = tracker.latest_message.get('intent', {}).get('name', '')
        latest_message = tracker.latest_message.get('text', '')

        # If the intent is ask_about_service or similar information-seeking intent
        if latest_intent == 'ask_about_service' or "what is" in latest_message.lower() or "tell me about" in latest_message.lower():
            # We'll handle service information separately
            # Keep the slot empty to continue asking later
            return {"service_type": None}

        if slot_value:
            # Convert the extracted slot value to lowercase for case-insensitive comparison
            slot_value_lower = str(slot_value).lower().strip()
            logger.debug(f"Validating lowercase service_type = {slot_value_lower}")

            # First try direct lookup in our variations dictionary
            if slot_value_lower in SERVICE_VARIATIONS:
                official_service_name = SERVICE_VARIATIONS[slot_value_lower]
                logger.debug(f"Service '{slot_value}' matched to '{official_service_name}'")
                return {"service_type": official_service_name}

            # If no direct match, try a fuzzy match
            for valid_service in VALID_SERVICES:
                valid_service_lower = valid_service.lower()
                if slot_value_lower in valid_service_lower or valid_service_lower in slot_value_lower:
                    logger.debug(f"Service '{slot_value}' fuzzy matched to '{valid_service}'.")
                    return {"service_type": valid_service}

            # No match found
            logger.debug(f"Service '{slot_value}' is NOT a valid service.")
            dispatcher.utter_message(text=f"Sorry, '{slot_value}' doesn't seem to be one of our services. We offer: {', '.join(VALID_SERVICES)}. Which service are you interested in?")
            return {"service_type": None}
        else:
            dispatcher.utter_message(text="Sorry, I didn't understand the service type. Could you please specify one of our services?")
            return {"service_type": None}

    def validate_appointment_datetime(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> Dict[Text, Any]:
        logger.debug(f"Attempting to validate appointment_datetime with input: \"{slot_value}\"")

        current_stored_date_str = tracker.get_slot("stored_date")
        current_stored_time_str = tracker.get_slot("stored_time")
        logger.debug(f"Current slot values - stored_date: \"{current_stored_date_str}\", stored_time: \"{current_stored_time_str}\"")

        user_input = str(slot_value).strip()
        parsed_input_dt = dateparser.parse(user_input, settings={'PREFER_DATES_FROM': 'future', 'RETURN_AS_TIMEZONE_AWARE': False})
        logger.debug(f"Dateparser parsed user_input \"{user_input}\" to: {parsed_input_dt}")

        # Fix: When we have a stored date and receive a time input, use the stored date
        if current_stored_date_str and parsed_input_dt:
            # Check if input is likely a time-only input
            if "am" in user_input.lower() or "pm" in user_input.lower() or ":" in user_input:
                # Parse the stored date
                stored_date_obj = dateparser.parse(current_stored_date_str)
                if stored_date_obj:
                    # Combine the stored date with the new time
                    combined_dt = datetime.combine(stored_date_obj.date(), parsed_input_dt.time())
                    if 9 <= combined_dt.hour < 18:
                        final_appointment_dt_str = combined_dt.strftime("%A, %B %d at %I:%M %p")
                        logger.info(f"Successfully combined stored date with time input: {final_appointment_dt_str}")
                        return {"appointment_datetime": final_appointment_dt_str, "stored_date": None, "stored_time": None}

        final_appointment_dt_str = None
        next_stored_date_str = current_stored_date_str
        next_stored_time_str = current_stored_time_str
        ask_for_more_info = False

        if parsed_input_dt:
            # Check if the parsed input contains both date and time information meaningfully
            # dateparser might fill in current date for time-only input, or midnight for date-only.
            # A simple check: year is not 1 (default for some relative times) and time is not midnight
            is_full_datetime_input = (
                parsed_input_dt.year > 1 and
                not (parsed_input_dt.hour == 0 and parsed_input_dt.minute == 0 and parsed_input_dt.second == 0)
            )
            is_date_only_input = (
                parsed_input_dt.year > 1 and
                (parsed_input_dt.hour == 0 and parsed_input_dt.minute == 0 and parsed_input_dt.second == 0)
            )
            # is_time_only_input can be inferred if not the above and time components are present

            if is_full_datetime_input:
                logger.debug(f"Input \"{user_input}\" was parsed as a full datetime: {parsed_input_dt}")
                if 9 <= parsed_input_dt.hour < 18:
                    final_appointment_dt_str = parsed_input_dt.strftime("%A, %B %d at %I:%M %p")
                    next_stored_date_str = None # Clear temporary slots
                    next_stored_time_str = None
                    logger.info(f"Successfully set full appointment_datetime: {final_appointment_dt_str}")
                else:
                    dispatcher.utter_message(text="Our clinic hours are 9 AM to 6 PM. Please provide a time within this range for your selected date.")
                    # Keep existing stored values, reject this input for appointment_datetime
                    return {"appointment_datetime": None, "stored_date": current_stored_date_str, "stored_time": current_stored_time_str}

            elif is_date_only_input:
                new_date_obj = parsed_input_dt.date()
                logger.debug(f"Input \"{user_input}\" was parsed as a date: {new_date_obj}")
                next_stored_date_str = new_date_obj.strftime("%A, %B %d, %Y") # Store full year for clarity
                if current_stored_time_str:
                    logger.debug(f"Combining new date \"{next_stored_date_str}\" with stored_time \"{current_stored_time_str}\"")
                    parsed_time_obj_from_stored = dateparser.parse(current_stored_time_str)
                    if parsed_time_obj_from_stored:
                        combined_dt = datetime.combine(new_date_obj, parsed_time_obj_from_stored.time())
                        if 9 <= combined_dt.hour < 18:
                            final_appointment_dt_str = combined_dt.strftime("%A, %B %d at %I:%M %p")
                            next_stored_date_str = None # Clear temporary slots
                            next_stored_time_str = None
                            logger.info(f"Successfully combined stored_time with new date: {final_appointment_dt_str}")
                        else:
                            dispatcher.utter_message(text=f"The stored time {current_stored_time_str} is outside clinic hours (9 AM - 6 PM) for the date {next_stored_date_str}.")
                            # Keep new date, but invalidate stored time as it's problematic
                            next_stored_time_str = None
                            ask_for_more_info = True # Will re-ask for time
                            dispatcher.utter_message(text=f"Okay, I have the date as {next_stored_date_str}. What time would you like for this date?")
                    else: # Should not happen if stored_time_str was valid
                        logger.warning(f"Could not parse stored_time_str: {current_stored_time_str}")
                        ask_for_more_info = True
                        dispatcher.utter_message(text=f"Great! I have the date: {next_stored_date_str}. What time would you prefer?")
                else:
                    ask_for_more_info = True
                    dispatcher.utter_message(text=f"Great! I have the date: {next_stored_date_str}. What time would you prefer?")

            else: # Likely a time-only input, or dateparser made a less specific guess
                logger.debug(f"Input \"{user_input}\" is likely time-only or ambiguous, parsed as {parsed_input_dt}")
                parsed_time_component = parsed_input_dt.time()
                if not (9 <= parsed_time_component.hour < 18):
                    dispatcher.utter_message(text="Our clinic hours are 9 AM to 6 PM. Please provide a time within this range.")
                    # Keep existing stored_date, reset current time attempt
                    return {"appointment_datetime": None, "stored_date": current_stored_date_str, "stored_time": None if current_stored_date_str else current_stored_time_str}

                next_stored_time_str = parsed_time_component.strftime("%I:%M %p").lstrip("0")
                logger.debug(f"Parsed time component as: {next_stored_time_str}")

                if current_stored_date_str:
                    logger.debug(f"Combining stored_date \"{current_stored_date_str}\" with new time \"{next_stored_time_str}\"")

                    # Try to parse the stored date - first with dateparser
                    parsed_date_obj_from_stored = None

                    # Check if stored_date is in YYYY-MM-DD format (from ActionCheckAvailability)
                    if re.match(r'^\d{4}-\d{2}-\d{2}$', current_stored_date_str):
                        try:
                            # Parse directly from YYYY-MM-DD format
                            year, month, day = map(int, current_stored_date_str.split('-'))
                            parsed_date_obj_from_stored = datetime(year, month, day)
                            logger.debug(f"Parsed stored date directly from YYYY-MM-DD format: {parsed_date_obj_from_stored}")
                        except Exception as e:
                            logger.error(f"Error parsing YYYY-MM-DD date: {e}")

                    # If direct parsing failed, try dateparser
                    if not parsed_date_obj_from_stored:
                        parsed_date_obj_from_stored = dateparser.parse(current_stored_date_str)

                    if parsed_date_obj_from_stored:
                        combined_dt = datetime.combine(parsed_date_obj_from_stored.date(), parsed_time_component)
                        final_appointment_dt_str = combined_dt.strftime("%A, %B %d at %I:%M %p")
                        next_stored_date_str = None # Clear temporary slots
                        next_stored_time_str = None
                        logger.info(f"Successfully combined stored_date with new time: {final_appointment_dt_str}")
                    else: # Should not happen
                        logger.error(f"Could not parse stored_date_str: {current_stored_date_str} during time combination.")
                        # This case is problematic, revert to asking
                        ask_for_more_info = True
                        next_stored_date_str = None # Clear potentially corrupt stored date
                        dispatcher.utter_message(text=f"I have the time as {next_stored_time_str}, but I need the date again, please.")
                else:
                    # Check if we have a stored_date from a previous availability check
                    stored_date = tracker.get_slot("stored_date")
                    if stored_date:
                        # We have a stored date from a previous availability check
                        try:
                            # Try to parse the stored date (which should be in YYYY-MM-DD format)
                            if re.match(r'^\d{4}-\d{2}-\d{2}$', stored_date):
                                year, month, day = map(int, stored_date.split('-'))
                                parsed_date = date(year, month, day)
                                # Combine with the time
                                combined_dt = datetime.combine(parsed_date, parsed_time_component)
                                final_appointment_dt_str = combined_dt.strftime("%A, %B %d at %I:%M %p")
                                next_stored_date_str = None  # Clear temporary slots
                                next_stored_time_str = None
                                ask_for_more_info = False
                                logger.info(f"Using stored_date from availability check with time: {final_appointment_dt_str}")
                                dispatcher.utter_message(text=f"Great! I'll book your appointment for {final_appointment_dt_str}.")
                            else:
                                # If not in expected format, ask for the date
                                ask_for_more_info = True
                                dispatcher.utter_message(text=f"Great! I have the time: {next_stored_time_str}. What date would you like?")
                        except Exception as e:
                            logger.error(f"Error using stored_date: {e}")
                            ask_for_more_info = True
                            dispatcher.utter_message(text=f"Great! I have the time: {next_stored_time_str}. What date would you like?")
                    else:
                        # No stored date, ask for one
                        ask_for_more_info = True
                        dispatcher.utter_message(text=f"Great! I have the time: {next_stored_time_str}. What date would you like?")
        else:
            logger.warning(f"Dateparser could not parse input: \"{user_input}\"")
            dispatcher.utter_message(text="I couldn't quite understand that date or time. Could you please try again? For example, 'tomorrow at 3 PM' or 'July 10th'.")
            # Return None for appointment_datetime, keep existing stored slots if user made a typo
            return {"appointment_datetime": None, "stored_date": current_stored_date_str, "stored_time": current_stored_time_str}

        if final_appointment_dt_str:
            return {
                "appointment_datetime": final_appointment_dt_str,
                "stored_date": None, # Ensure cleared
                "stored_time": None  # Ensure cleared
            }
        else:
            # If we are here, it means we need more information or an error occurred
            # The dispatcher messages should have already been sent.
            # We update stored_date and stored_time based on what was parsed or kept.
            logger.debug(f"Validation incomplete. Returning: appointment_datetime: None, stored_date: \"{next_stored_date_str}\", stored_time: \"{next_stored_time_str}\"")
            return {
                "appointment_datetime": None,
                "stored_date": next_stored_date_str,
                "stored_time": next_stored_time_str
            }

    # Validation method for 'name' slot with enhanced confidence handling
    def validate_name(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> Dict[Text, Any]:
        """Validate name value with enhanced confidence handling."""
        logger.debug(f"Validating name = {slot_value}")

        # Check if we're in a confirmation flow for a previously detected name
        last_intent = tracker.get_intent_of_latest_message()
        confirming_name = tracker.get_slot("confirming_name")
        name_candidate = tracker.get_slot("name_candidate")

        # Handle confirmation response
        if confirming_name and name_candidate:
            if last_intent == "affirm":
                logger.info(f"User confirmed name: {name_candidate}")
                return {"name": name_candidate, "confirming_name": None, "name_candidate": None}
            elif last_intent == "deny":
                logger.info("User denied the suggested name, asking again")
                # Improved prompt with clearer instructions
                dispatcher.utter_message(text="I apologize for the confusion. Please tell me your full name (first and last name).")
                return {"name": None, "confirming_name": None, "name_candidate": None}

        # Get confidence metadata from the message if available
        latest_message = tracker.latest_message
        name_confidence_metadata = latest_message.get("name_confidence_metadata", {})

        if name_confidence_metadata:
            confidence = name_confidence_metadata.get("name_confidence", 0.0)
            needs_confirmation = name_confidence_metadata.get("name_needs_confirmation", True)
            detected_name = name_confidence_metadata.get("name_candidate", "")
            is_ambiguous = name_confidence_metadata.get("is_ambiguous_name", False)
            is_name_context = name_confidence_metadata.get("is_name_context", False)
            is_fallback = name_confidence_metadata.get("is_fallback_detection", False)
            models_agreed = name_confidence_metadata.get("models_agreed", False)

            logger.debug(f"Name confidence metadata: confidence={confidence}, needs_confirmation={needs_confirmation}, "
                        f"name={detected_name}, is_ambiguous={is_ambiguous}, is_name_context={is_name_context}, "
                        f"models_agreed={models_agreed}")

            # If we have a detected name
            if detected_name:
                if needs_confirmation:
                    # Customize confirmation message based on confidence, ambiguity, and model agreement
                    if is_ambiguous:
                        # For ambiguous names, be explicit about the ambiguity
                        dispatcher.utter_message(text=f"'{detected_name}' can be both a name and a common word. Is {detected_name} your name?")
                    elif is_fallback:
                        # For fallback detection (when we're using raw input in name context)
                        dispatcher.utter_message(text=f"Just to confirm, is '{detected_name}' your name?")
                    elif models_agreed and confidence >= 0.7:
                        # Both models agreed with high confidence
                        dispatcher.utter_message(text=f"Your name is {detected_name}, correct?")
                    elif confidence >= 0.6:
                        # For medium-high confidence
                        dispatcher.utter_message(text=f"Your name is {detected_name}, correct?")
                    elif confidence >= 0.4:
                        # For medium confidence
                        dispatcher.utter_message(text=f"I think your name is {detected_name}. Is that correct?")
                    else:
                        # For low confidence
                        dispatcher.utter_message(text=f"I'm not sure if I understood correctly. Is your name {detected_name}?")

                    return {"name": None, "confirming_name": True, "name_candidate": detected_name}
                else:
                    # High confidence, accept directly
                    if models_agreed:
                        logger.info(f"Accepting high-confidence name with model agreement: {detected_name} ({confidence})")
                    else:
                        logger.info(f"Accepting high-confidence name: {detected_name} ({confidence})")
                    return {"name": detected_name}

        # Fall back to standard validation if no metadata or no name detected
        if slot_value and len(str(slot_value).strip()) > 1:
            # For very short inputs that might not be names, ask for confirmation
            if len(str(slot_value).strip()) <= 3:
                dispatcher.utter_message(text=f"Is '{slot_value}' your name?")
                return {"name": None, "confirming_name": True, "name_candidate": slot_value}
            else:
                # For longer inputs, accept directly
                return {"name": slot_value, "confirming_name": None, "name_candidate": None}
        else:
            # Improved prompt with clearer instructions
            dispatcher.utter_message(text="Please enter your full name (first and last name).")
            return {"name": None}

    def validate_phone_number(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> Dict[Text, Any]:
        """Validate phone_number and trigger form submission."""
        if not slot_value:
            dispatcher.utter_message(text="Please provide a valid phone number.")
            return {"phone_number": None}

        cleaned = ''.join(filter(str.isdigit, str(slot_value)))

        if 10 <= len(cleaned) <= 12:
            return {
                "phone_number": cleaned
            }
        else:
            dispatcher.utter_message(text="Please provide a valid 10-digit phone number.")
            return {"phone_number": None}

    # Helper method to fix time format (from current actions.py)
    def _fix_time_format(self, time_str):
        """Helper function to fix common time format issues for dateparser"""
        time_str = str(time_str).lower().strip()

        # Add missing "pm" to numbers that are likely afternoon times
        if re.match(r'^\d{1,2}$', time_str):
            hour = int(time_str)
            # Simple heuristic: Assume times from 1-6 are PM, 7-12 are AM
            # This can be refined based on typical clinic hours or user patterns
            if 1 <= hour <= 6:
                time_str = f"{hour}pm"
            elif 7 <= hour <= 12:
                time_str = f"{hour}am"
            # Note: hour 0 or > 12 are not handled by this simple heuristic

        # Fix formats like "3" to "3:00pm" (if AM/PM is determined)
        elif re.match(r'^\d{1,2}pm$', time_str) or re.match(r'^\d{1,2}am$', time_str):
            parts = re.match(r'^(\d{1,2})(am|pm)$', time_str)
            if parts:
                hour, ampm = parts.groups()
                # Only add :00 if it's just an hour number followed by am/pm
                if ':' not in hour:
                    time_str = f"{hour}:00{ampm}"

        # Handle "afternoon", "morning", "evening" terms (simple mapping)
        if time_str == "afternoon":
            time_str = "3:00pm" # Arbitrary default afternoon time
        elif time_str == "morning":
            time_str = "10:00am" # Arbitrary default morning time
        elif time_str == "evening":
            time_str = "6:00pm" # Arbitrary default evening time

        return time_str

    # Helper method for manual datetime parsing (from current actions.py)
    def _manual_datetime_parse(self, date_str, time_str):
        """Manual datetime parsing for common formats when dateparser fails"""
        logger.debug(f"Attempting manual parse for date: '{date_str}', time: '{time_str}'") # Using logger

        # Current date info
        now = datetime.now()
        year = now.year

        # Process date string
        date_str = str(date_str).lower().strip()

        # Handle relative day expressions
        if date_str == "today":
            target_date = now.date()
        elif date_str == "tomorrow":
            target_date = now.date() + timedelta(days=1)
        elif date_str == "yesterday": # Although we prefer future, good to handle for robustness
            target_date = now.date() - timedelta(days=1)
        # Handle specific weekdays (find the next occurrence)
        elif date_str in ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]:
            weekday_map = {
                "monday": 0, "tuesday": 1, "wednesday": 2, "thursday": 3,
                "friday": 4, "saturday": 5, "sunday": 6
            }
            target_weekday = weekday_map[date_str]
            days_ahead = (target_weekday - now.weekday() + 7) % 7
            # If days_ahead is 0, it's today. We need the *next* occurrence for future bookings.
            if days_ahead == 0:
                days_ahead = 7
            target_date = now.date() + timedelta(days=days_ahead)

        # Handle day + month or month + day formats
        else:
            month_dict = {
                "jan": 1, "january": 1,
                "feb": 2, "february": 2,
                "mar": 3, "march": 3,
                "apr": 4, "april": 4,
                "may": 5,
                "jun": 6, "june": 6,
                "jul": 7, "july": 7,
                "aug": 8, "august": 8,
                "sep": 9, "september": 9,
                "oct": 10, "october": 10,
                "nov": 11, "november": 11,
                "dec": 12, "december": 12
            }

            day = None
            month = None

            # Check for "20 may" format
            day_month_match = re.search(r'(\d+)\s*(?:of\s*)?([a-z]+)', date_str)
            if day_month_match:
                day_str, month_str = day_month_match.groups()
                day = int(day_str)
                for key in month_dict:
                    if key in month_str:
                        month = month_dict[key]
                        break

            # Check for "may 20" format
            month_day_match = re.search(r'([a-z]+)\s*(\d+)', date_str)
            if not month and month_day_match:
                month_str, day_str = month_day_match.groups()
                day = int(day_str)
                for key in month_dict:
                    if key in month_str:
                        month = month_dict[key]
                        break

            if day and month:
                # Create date
                try:
                    target_date = datetime(year, month, day).date()
                    # If the date is in the past, use next year
                    if target_date < now.date():
                        target_date = datetime(year + 1, month, day).date()
                except ValueError:
                    logger.debug(f"Manual date parsing failed for: {date_str}")
                    return None  # Invalid date
            else:
                logger.debug(f"Manual date parsing failed for: {date_str}")
                return None  # Couldn't parse the date


        # Process time string
        time_str = str(time_str).lower().strip()

        # Extract hours and minutes
        hour = 12  # Default
        minute = 0  # Default
        is_pm = False

        # Check for AM/PM indicators
        if "pm" in time_str or "p.m." in time_str:
            is_pm = True
        elif "am" in time_str or "a.m." in time_str:
            is_pm = False # Explicitly set is_pm to False for AM

        # Extract numeric parts
        time_parts = re.search(r'(\d{1,2})(?::(\d{1,2}))?', time_str)
        if time_parts:
            hour = int(time_parts.group(1))
            if time_parts.group(2):
                minute = int(time_parts.group(2))

            # Adjust hour for PM (only if it's not 12 and is pm)
            if is_pm and hour < 12:
                hour += 12
            # Adjust for 12am (midnight)
            if hour == 12 and not is_pm:
                hour = 0
        else:
            logger.debug(f"Manual time parsing failed for: {time_str}")
            return None  # Couldn't parse the time

        # Create the full datetime
        try:
            combined_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=hour, minute=minute))
            logger.debug(f"Manual parse successful: {combined_datetime}")
            return combined_datetime
        except Exception as e:
            logger.debug(f"Error combining date and time in manual parse: {e}")
            return None

class ActionResetFormState(Action):
    def name(self) -> Text:
        return "action_reset_form_state"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        """Reset all form slots to recover from errors."""

        logger.debug("Resetting all form slots to recover from error state")

        # Optional: Send a message to the user that we're resetting
        # dispatcher.utter_message(text="Let me start fresh. How can I help you?")

        # Clear all slots related to appointment booking
        return [SlotSet("service_type", None),
                SlotSet("appointment_datetime", None),
                SlotSet("name", None),
                SlotSet("phone_number", None),
                SlotSet("stored_date", None),
                SlotSet("stored_time", None),
                SlotSet("confirming_name", None),
                SlotSet("name_candidate", None),
                SlotSet("appointment_id", None),
                SlotSet("requested_slot", None)]  # Also reset the requested_slot

# The ValidateAppointmentVerificationForm class has been moved to validate_appointment_verification_form.py
# Note: This class is imported directly by the Rasa SDK based on the action name in domain.yml
# We don't need to import it here to avoid circular dependencies

class ActionCheckAvailability(Action):
    def name(self) -> Text:
        return "action_check_availability"

    async def run(self, dispatcher: CollectingDispatcher,
                  tracker: Tracker,
                  domain: DomainDict) -> List[EventType]:

        # Get the intent to differentiate between clinic hours and availability check
        latest_intent = tracker.latest_message.get('intent', {}).get('name', '')
        latest_message_text = tracker.latest_message.get('text', '').lower()

        # If the intent is ask_clinic_hours or the message contains keywords about clinic hours
        # rather than specific availability, respond with clinic hours
        if latest_intent == 'ask_clinic_hours' or any(keyword in latest_message_text for keyword in
                                                    ['operating hours', 'clinic hours', 'when are you open',
                                                     'working hours', 'business hours']):
            dispatcher.utter_message(response="utter_ask_clinic_hours")
            return []

        requested_date_str = None

        # Try to get date from the latest entity values first
        date_entity = next(tracker.get_latest_entity_values("date"), None)
        if date_entity:
            requested_date_str = str(date_entity)
            logger.debug(f"Date entity found from tracker.get_latest_entity_values: {requested_date_str}")

        # If not found, check all entities in the latest message (fallback)
        if not requested_date_str:
            entities = tracker.latest_message.get("entities", [])
            for ent in entities:
                if ent["entity"] == "date":
                    requested_date_str = str(ent["value"])
                    logger.debug(f"Date entity found from latest_message.entities: {requested_date_str}")
                    break

        # If still not found, try to extract date from the message text
        if not requested_date_str:
            # Try to extract date patterns from the message
            message_text = tracker.latest_message.get('text', '').lower()

            # Check for common date formats in the message
            date_patterns = [
                r'(\d{1,2})\s*(st|nd|rd|th)?\s*(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec|january|february|march|april|may|june|july|august|september|october|november|december)',
                r'(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec|january|february|march|april|may|june|july|august|september|october|november|december)\s*(\d{1,2})\s*(st|nd|rd|th)?',
                r'(\d{1,2})[/-](\d{1,2})[/-](\d{2,4})',
                r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})'
            ]

            for pattern in date_patterns:
                match = re.search(pattern, message_text)
                if match:
                    requested_date_str = match.group(0)
                    logger.debug(f"Date pattern found in message text: {requested_date_str}")
                    break

        # If we still don't have a date, ask the user
        if not requested_date_str:
            dispatcher.utter_message(response="utter_ask_date_for_availability")
            return []

        logger.debug(f"ActionCheckAvailability: Checking for date: {requested_date_str}")

        try:
            parsed_date_obj = dateparser.parse(requested_date_str, settings={'PREFER_DATES_FROM': 'future'})
            if not parsed_date_obj:
                logger.warning(f"Could not parse date: {requested_date_str}")
                dispatcher.utter_message(response="utter_invalid_date_for_availability")
                return []

            appointment_date = parsed_date_obj.date()
            formatted_date_for_user = appointment_date.strftime("%A, %B %d, %Y")
        except Exception as e:
            logger.error(f"Error parsing date string '{requested_date_str}': {e}")
            dispatcher.utter_message(response="utter_invalid_date_for_availability")
            return []

        conn = None
        try:
            conn = get_db_connection() # Assumes get_db_connection is defined in your actions.py
            if not conn:
                logger.error("Failed to connect to the database for availability check.")
                dispatcher.utter_message(response="utter_failed_to_check_availability")
                return []

            cur = conn.cursor()
            # Query to get available (non-booked) start times for the given date
            cur.execute(
                """
                SELECT start_time FROM doctor_availability
                WHERE DATE(start_time) = %s AND is_booked = FALSE
                ORDER BY start_time;
                """,
                (appointment_date,)
            )
            available_slots_db = cur.fetchall()

            if available_slots_db:
                # Format times to be user-friendly, e.g., "3:00 PM", "4:30 PM"
                # Also log the raw times from the database for debugging
                logger.debug(f"Raw available slots from DB: {[slot['start_time'] for slot in available_slots_db]}")

                # Format times for display, adjusting for timezone if needed
                times_list = []
                for slot in available_slots_db:
                    # Get the hour and minute directly from the database time
                    slot_hour = slot['start_time'].hour
                    slot_minute = slot['start_time'].minute

                    # Format the time in 12-hour format with AM/PM
                    if slot_hour == 0:
                        formatted_time = f"12:{slot_minute:02d} AM"
                    elif slot_hour < 12:
                        formatted_time = f"{slot_hour}:{slot_minute:02d} AM"
                    elif slot_hour == 12:
                        formatted_time = f"12:{slot_minute:02d} PM"
                    else:
                        formatted_time = f"{slot_hour-12}:{slot_minute:02d} PM"

                    # Remove leading zero for hours
                    formatted_time = formatted_time.lstrip("0")

                    times_list.append(formatted_time)

                times_list_str = ", ".join(times_list)
                dispatcher.utter_message(response="utter_availability_on_date", date=formatted_date_for_user, times_list=times_list_str)

                # Store the requested date in a slot for later use when the user selects a time
                # This is the key fix - we store the date the user asked about
                return [SlotSet("stored_date", appointment_date.strftime("%Y-%m-%d"))]
            else:
                dispatcher.utter_message(response="utter_no_availability_on_date", date=formatted_date_for_user)
                return []

        except psycopg2.Error as db_err:
            logger.error(f"Database query error during availability check: {db_err}")
            dispatcher.utter_message(response="utter_failed_to_check_availability")
            return []
        except Exception as e:
            logger.error(f"Unexpected error during availability check: {e}")
            dispatcher.utter_message(response="utter_failed_to_check_availability")
            return []
        finally:
            if conn:
                cur.close() # Close cursor
                conn.close()
                logger.debug("Database connection closed after availability check.")

class ActionSubmitAppointmentVerification(Action):
    def name(self) -> Text:
        return "action_submit_appointment_verification"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[EventType]:
        logger.debug("ActionSubmitAppointmentVerification: Deactivating form, clearing slots, and confirming.")

        # Utter a confirmation message that verification is complete
        dispatcher.utter_message(response="utter_verification_success")

        return [
            ActiveLoop(None),  # Deactivate the form
            SlotSet("appointment_id", None),      # Clear appointment_id slot
            SlotSet("phone_number", None),      # Clear phone_number slot
            SlotSet("requested_slot", None)     # Ensure requested_slot is cleared from tracker state
        ]
