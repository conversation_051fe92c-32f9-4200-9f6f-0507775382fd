"""
Database utilities for the appointment verification system.
This file contains functions for connecting to the database and other database-related utilities.
"""

import os
import logging
import psycopg2
from psycopg2 import extras

logger = logging.getLogger(__name__)

# Database configuration using environment variables for security
DB_HOST = os.environ.get("DB_HOST", "localhost")
DB_NAME = os.environ.get("DB_NAME", "arham_bot_db")
DB_USER = os.environ.get("DB_USER", "postgres")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "neel")  # Updated default password
DB_PORT = os.environ.get("DB_PORT", "5432")

def get_db_connection():
    """
    Creates and returns a connection to the PostgreSQL database.
    Uses environment variables for connection parameters.
    Returns a connection object with RealDictCursor for dictionary-like row access.
    """
    try:
        logger.debug(f"Attempting to connect to database: host={DB_HOST}, db={DB_NAME}, user={DB_USER}, port={DB_PORT}")
        connection = psycopg2.connect(
            host=DB_HOST,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            port=DB_PORT,
            cursor_factory=extras.RealDictCursor
        )
        logger.debug("Successfully connected to the database")

        # Test the connection by executing a simple query
        cur = connection.cursor()
        cur.execute("SELECT version();")
        version = cur.fetchone()
        logger.debug(f"Database version: {version}")
        cur.close()

        return connection
    except psycopg2.Error as e:
        logger.error(f"Database connection error: {e}")
        return None
