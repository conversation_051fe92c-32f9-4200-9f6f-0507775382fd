"""
Form validation for the appointment cancellation form.
This file handles the validation of appointment_id and phone_number slots
independently to prevent overwriting valid values, and adds a confirmation step
before cancellation.
"""

from typing import Any, Text, Dict, List
import logging
import re

from rasa_sdk import Tracker, FormValidationAction
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.types import DomainDict
from rasa_sdk.events import SlotSet, ActiveLoop

# Import database connection function from db_utils.py
from actions.db_utils import get_db_connection

logger = logging.getLogger(__name__)

class ValidateAppointmentCancellationForm(FormValidationAction):
    """
    Form validation action for the appointment cancellation form.

    This class validates the appointment_id and phone_number slots independently,
    ensuring that valid values are preserved even if other fields fail validation.
    It also adds a confirmation step before cancellation.
    """

    def name(self) -> Text:
        return "validate_appointment_cancellation_form"

    def validate_appointment_id(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> Dict[Text, Any]:
        """
        Validate the appointment_id slot.

        This function validates the appointment ID independently of the phone number,
        ensuring that a valid appointment ID is preserved even if the phone number
        validation fails.
        """
        logger.debug(f"Validating appointment_id for cancellation = {slot_value}")

        # Get the current phone number to preserve it
        current_phone = tracker.get_slot("phone_number")
        logger.debug(f"Current phone_number slot value: {current_phone}")

        # If no value was provided, ask for it
        if not slot_value:
            dispatcher.utter_message(response="utter_ask_cancellation_appointment_id")
            # Return None for appointment_id but preserve phone_number
            return {"appointment_id": None, "phone_number": current_phone}

        # Extract only digits from the input
        # This helps filter out any text that might be included with the ID
        input_text = str(slot_value)
        cleaned_id = ''.join(filter(str.isdigit, input_text))

        # If no digits were found, reject the input
        if not cleaned_id:
            dispatcher.utter_message(text="Please provide a valid appointment ID number.")
            # Return None for appointment_id but preserve phone_number
            return {"appointment_id": None, "phone_number": current_phone}

        # Check if the input looks like a phone number (10+ digits)
        # This is a critical check to prevent phone numbers from being treated as appointment IDs
        if len(cleaned_id) >= 10:
            logger.warning(f"Input '{cleaned_id}' looks like a phone number, not an appointment ID")
            dispatcher.utter_message(text="That looks like a phone number, not an appointment ID. Please provide your appointment ID number (usually a shorter number).")
            # Return None for appointment_id but preserve phone_number
            return {"appointment_id": None, "phone_number": current_phone}

        # Check if the appointment ID exists in the database
        conn = None
        try:
            conn = get_db_connection()
            if not conn:
                logger.error("Failed to connect to the database for appointment ID verification.")
                dispatcher.utter_message(text="I'm having trouble verifying your appointment ID. Please try again later.")
                # Return None for appointment_id but preserve phone_number
                return {"appointment_id": None, "phone_number": current_phone}

            cur = conn.cursor()
            cur.execute(
                """
                SELECT appointment_id FROM appointments
                WHERE appointment_id = %s AND status = 'booked'
                """,
                (cleaned_id,)
            )

            appointment = cur.fetchone()

            if not appointment:
                dispatcher.utter_message(text="I couldn't find an active appointment with that ID. Please check and try again.")
                # Return None for appointment_id but preserve phone_number
                return {"appointment_id": None, "phone_number": current_phone}

            # ID exists, proceed to phone verification
            dispatcher.utter_message(text=f"Thank you. I found your appointment with ID {cleaned_id}. Now I need to verify your identity before cancellation.")
            # Ask for the phone number
            dispatcher.utter_message(response="utter_ask_cancellation_phone")
            # Return the validated appointment ID and set the next requested slot
            return {
                "appointment_id": cleaned_id,
                "requested_slot": "phone_number"
            }

        except Exception as e:
            logger.error(f"Database error during appointment ID verification for cancellation: {e}")
            dispatcher.utter_message(text="I'm having trouble verifying your appointment ID. Please try again later.")
            # Return None for appointment_id but preserve phone_number
            return {"appointment_id": None, "phone_number": current_phone}
        finally:
            if conn:
                conn.close()

    def validate_phone_number(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> Dict[Text, Any]:
        """Validate phone number for cancellation."""
        current_appointment_id = tracker.get_slot("appointment_id")
        
        if not current_appointment_id:
            dispatcher.utter_message(text="I need your appointment ID first before I can verify your phone number.")
            return {"phone_number": None}
            
        # Clean the phone number (remove all non-digit characters)
        cleaned_phone = ''.join(filter(str.isdigit, str(slot_value)))
        
        # Basic validation for phone number length
        if not cleaned_phone or len(cleaned_phone) < 10:
            dispatcher.utter_message(text="Please provide a valid 10-digit phone number.")
            return {"phone_number": None}

        # Verify that the phone number matches the appointment ID in the database
        conn = None
        try:
            conn = get_db_connection()
            if not conn:
                logger.error("Failed to connect to the database for phone verification.")
                dispatcher.utter_message(text="I'm having trouble verifying your phone number. Please try again later.")
                return {"phone_number": None}

            cur = conn.cursor()
            cur.execute(
                """
                SELECT a.appointment_id, a.service_type, a.appointment_date, 
                       a.appointment_time, d.name as doctor_name, u.name as patient_name
                FROM appointments a
                JOIN users u ON a.user_id = u.user_id
                JOIN doctors d ON a.doctor_id = d.doctor_id
                WHERE a.appointment_id = %s AND u.phone = %s AND a.status = 'booked'
                """,
                (current_appointment_id, cleaned_phone)
            )

            appointment = cur.fetchone()

            if not appointment:
                dispatcher.utter_message(text="The phone number you provided doesn't match our records for this appointment ID. Please check and try again.")
                return {"phone_number": None}

            # If we get here, verification is successful
            # Format the appointment details for display
            appointment_date = appointment['appointment_date']
            appointment_time = appointment['appointment_time']
            formatted_date = appointment_date.strftime("%A, %B %d, %Y")
            formatted_time = appointment_time.strftime("%I:%M %p").lstrip("0")
            service_type = appointment['service_type']
            doctor_name = appointment['doctor_name']
            patient_name = appointment['patient_name']

            # Send the appointment details to the user
            dispatcher.utter_message(
                text=f"Here are your appointment details, {patient_name}:\n\n"
                     f"Appointment ID: {current_appointment_id}\n"
                     f"Service: {service_type}\n"
                     f"Date: {formatted_date}\n"
                     f"Time: {formatted_time}\n"
                     f"Doctor: {doctor_name}"
            )

            # Ask for confirmation before cancellation
            dispatcher.utter_message(response="utter_confirm_cancellation")

            # Return the validated phone number and proceed to confirmation
            return {
                "phone_number": cleaned_phone,
                "requested_slot": "confirm_cancellation"
            }

        except Exception as e:
            logger.error(f"Error during phone verification for cancellation: {e}")
            dispatcher.utter_message(text="I'm having trouble verifying your phone number. Please try again later.")
            return {"phone_number": None}
            
        finally:
            if conn:
                conn.close()

    def validate_confirm_cancellation(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> Dict[Text, Any]:
        """Validate the confirmation for cancellation."""
        # The slot value will be set automatically based on the intent mapping in domain.yml
        # affirm intent -> true, deny intent -> false
        
        # Get the current values to preserve them
        current_appointment_id = tracker.get_slot("appointment_id")
        current_phone = tracker.get_slot("phone_number")
        
        # If the user confirms (slot_value is True)
        if slot_value:
            dispatcher.utter_message(text="Thank you for confirming. I'll proceed with cancelling your appointment.")
            return {"confirm_cancellation": True}
        
        # If the user denies (slot_value is False)
        else:
            dispatcher.utter_message(response="utter_cancellation_aborted")
            # Return all slots to signal form completion
            return {
                "confirm_cancellation": False,
                "requested_slot": None  # Signal form completion
            }

    def submit(
        self,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: Dict[Text, Any],
    ) -> List[Dict]:
        """Define what the form has to do after all required slots are filled."""
        # This method is called when the form is complete
        
        # Log that we're submitting the form
        logger.info("Submitting appointment cancellation form")
        
        # Check if the user confirmed the cancellation
        if tracker.get_slot("confirm_cancellation"):
            # If confirmed, the actual cancellation will be handled by action_submit_appointment_cancellation
            logger.info("User confirmed cancellation, proceeding to submit action")
        else:
            # If denied, we've already sent the abort message in validate_confirm_cancellation
            logger.info("User denied cancellation, aborting")
        
        # Return events that will deactivate the form and clear slots
        # The actual cancellation will be handled by action_submit_appointment_cancellation
        return [
            SlotSet("requested_slot", None),
            ActiveLoop(None)  # This explicitly deactivates the form
        ]
