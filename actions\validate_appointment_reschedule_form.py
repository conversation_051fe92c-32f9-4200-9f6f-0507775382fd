"""
Form validation action for appointment reschedule form.
This file handles validation of all slots in the reschedule form including
appointment ID verification, phone number validation, and new datetime parsing.
"""

from typing import Any, Text, Dict, List
import logging
import dateparser
from datetime import datetime, date
import re

from rasa_sdk import FormValidationAction, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.types import DomainDict
from rasa_sdk.events import SlotSet

logger = logging.getLogger(__name__)

# Mock appointment database for demo purposes
# TODO: Replace with actual database queries when integrating with database
MOCK_APPOINTMENTS = {
    "1001": {
        "appointment_id": "1001",
        "service_type": "Physical Therapy & Comprehensive Pain Management",
        "appointment_date": "Monday, May 20, 2024",
        "appointment_time": "10:00 AM",
        "doctor_name": "Dr. <PERSON>",
        "phone_number": "9123456789",
        "status": "booked"
    },
    "1002": {
        "appointment_id": "1002",
        "service_type": "Weight Loss Programs",
        "appointment_date": "Wednesday, May 22, 2024",
        "appointment_time": "2:00 PM",
        "doctor_name": "Dr. <PERSON>",
        "phone_number": "9876543210",
        "status": "booked"
    },
    "1003": {
        "appointment_id": "1003",
        "service_type": "Neurological Rehabilitation",
        "appointment_date": "Friday, May 24, 2024",
        "appointment_time": "4:00 PM",
        "doctor_name": "Dr. Emily Rodriguez",
        "phone_number": "9555666777",
        "status": "booked"
    },
    "1004": {
        "appointment_id": "1004",
        "service_type": "Orthopedic Rehabilitation",
        "appointment_date": "Tuesday, May 21, 2024",
        "appointment_time": "11:30 AM",
        "doctor_name": "Dr. James Wilson",
        "phone_number": "9444555666",
        "status": "booked"
    },
    "1005": {
        "appointment_id": "1005",
        "service_type": "Gynaecology Rehabilitation & Prenatal Care",
        "appointment_date": "Thursday, May 23, 2024",
        "appointment_time": "3:30 PM",
        "doctor_name": "Dr. Lisa Patel",
        "phone_number": "9333444555",
        "status": "booked"
    }
}

class ValidateAppointmentRescheduleForm(FormValidationAction):
    """
    Validates the appointment reschedule form slots.
    """

    def name(self) -> Text:
        return "validate_appointment_reschedule_form"

    def validate_reschedule_appointment_id(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> Dict[Text, Any]:
        """Validate the reschedule appointment ID."""
        logger.debug(f"Validating reschedule_appointment_id = {slot_value}")

        if slot_value:
            # For demo: Accept any appointment ID and show mock data
            appointment_id = str(slot_value).strip()
            
            if appointment_id in MOCK_APPOINTMENTS:
                appointment = MOCK_APPOINTMENTS[appointment_id]
                logger.debug(f"Found appointment in mock database: {appointment}")
                
                # Store mock appointment details in slots for display
                return {
                    "reschedule_appointment_id": appointment_id,
                    "mock_service_type": appointment["service_type"],
                    "mock_appointment_datetime": f"{appointment['appointment_date']} at {appointment['appointment_time']}",
                    "mock_doctor_name": appointment["doctor_name"]
                }
            else:
                # For demo: Accept any ID but use default mock data
                logger.debug(f"Appointment ID {appointment_id} not in mock database, using default")
                return {
                    "reschedule_appointment_id": appointment_id,
                    "mock_service_type": "Physical Therapy & Comprehensive Pain Management",
                    "mock_appointment_datetime": "Monday, May 20, 2024 at 10:00 AM",
                    "mock_doctor_name": "Dr. Sarah Johnson"
                }
        else:
            dispatcher.utter_message(text="Please provide a valid appointment ID.")
            return {"reschedule_appointment_id": None}

    def validate_reschedule_phone_number(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> Dict[Text, Any]:
        """Validate the phone number for reschedule."""
        logger.debug(f"Validating reschedule_phone_number = {slot_value}")

        if slot_value:
            phone_number = str(slot_value).strip()
            
            # For demo: Accept any phone number
            # TODO: Add actual phone number validation and database verification
            logger.debug(f"Accepting phone number for demo: {phone_number}")
            return {"reschedule_phone_number": phone_number}
        else:
            dispatcher.utter_message(text="Please provide a valid phone number.")
            return {"reschedule_phone_number": None}

    def validate_new_appointment_datetime(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> Dict[Text, Any]:
        """Validate the new appointment datetime."""
        logger.debug(f"Validating new_appointment_datetime = {slot_value}")

        if slot_value:
            try:
                # Parse the datetime string
                parsed_datetime = dateparser.parse(str(slot_value), settings={
                    'PREFER_DATES_FROM': 'future',
                    'RETURN_AS_TIMEZONE_AWARE': False
                })
                
                if parsed_datetime:
                    # Check if it's within clinic hours (9 AM to 6 PM)
                    if 9 <= parsed_datetime.hour < 18:
                        # Check if it's a future date
                        if parsed_datetime.date() >= datetime.now().date():
                            formatted_datetime = parsed_datetime.strftime("%A, %B %d, %Y at %I:%M %p")
                            logger.debug(f"Successfully parsed new datetime: {formatted_datetime}")
                            
                            return {
                                "new_appointment_datetime": formatted_datetime,
                                "new_stored_date": parsed_datetime.strftime("%Y-%m-%d"),
                                "new_stored_time": parsed_datetime.strftime("%I:%M %p")
                            }
                        else:
                            dispatcher.utter_message(text="Please provide a future date for your appointment.")
                            return {"new_appointment_datetime": None}
                    else:
                        dispatcher.utter_message(text="Our clinic hours are 9 AM to 6 PM. Please provide a time within this range.")
                        return {"new_appointment_datetime": None}
                else:
                    dispatcher.utter_message(text="I couldn't understand that date and time. Please try again with a format like 'tomorrow at 3pm' or 'May 25th at 2:30pm'.")
                    return {"new_appointment_datetime": None}
                    
            except Exception as e:
                logger.error(f"Error parsing new appointment datetime: {e}")
                dispatcher.utter_message(text="I couldn't understand that date and time. Please try again.")
                return {"new_appointment_datetime": None}
        else:
            dispatcher.utter_message(text="Please provide when you'd like to reschedule your appointment to.")
            return {"new_appointment_datetime": None}

    def validate_confirm_reschedule(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: DomainDict,
    ) -> Dict[Text, Any]:
        """Validate the reschedule confirmation."""
        logger.debug(f"Validating confirm_reschedule = {slot_value}")

        # The slot mapping handles this automatically based on affirm/deny intents
        return {"confirm_reschedule": slot_value}
