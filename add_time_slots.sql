-- Add time slots for May 8-10
INSERT INTO doctor_availability (doctor_id, start_time, end_time, is_booked)
SELECT 
    doctor_id,
    start_time,
    start_time + interval '1 hour',
    false
FROM (
    SELECT 
        doctor_id,
        date + time as start_time
    FROM doctors
    CROSS JOIN (
        SELECT date::date as date
        FROM generate_series('2024-05-08'::date, '2024-05-10'::date, '1 day'::interval) as date
    ) as dates
    CROSS JOIN (
        SELECT time::time as time
        FROM (VALUES 
            ('12:00:00'::time),
            ('13:00:00'::time),
            ('15:00:00'::time),
            ('17:00:00'::time)
        ) as times(time)
    ) as times
) as slots
ORDER BY doctor_id, start_time; 