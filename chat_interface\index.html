<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ARHAM - Chatbot</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="header-left">
                <img src="img/logo.png" alt="Bot Avatar" class="avatar bot-avatar-header" id="header-bot-avatar">
                <div class="header-info">
                    <h1>Arham AI Assistant</h1>
                    <div class="status-indicator">
                        <span class="status-dot online"></span>
                        <span class="status-text">Online</span>
                    </div>
                </div>
            </div>
            <!-- <div class="header-controls">
                <button class="header-button minimize" aria-label="Minimize">
                    <svg viewBox="0 0 24 24" width="24" height="24"><path d="M19 13H5v-2h14v2z"></path></svg>
                </button>
                <button class="header-button close" aria-label="Close">
                    <svg viewBox="0 0 24 24" width="24" height="24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"></path></svg>
                </button>
            </div> -->
        </div>
        <div class="chat-messages" id="chat-messages">
            <!-- Messages will be appended here by JavaScript -->
        </div>
        <div class="chat-input-area">
            <div class="input-container">
                <input type="text" id="user-input" placeholder="Type your message...">
                <button id="send-button" class="send-button">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24px" height="24px"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/></svg>
                </button>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>