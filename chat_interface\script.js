const chatMessages = document.getElementById('chat-messages');
const userInput = document.getElementById('user-input');
const sendButton = document.getElementById('send-button');
const headerBotAvatar = document.getElementById('header-bot-avatar');
// Remove these two lines
// const minimizeButton = document.querySelector('.header-button.minimize');
// const closeButton = document.querySelector('.header-button.close');

// Add Rasa server URL
const RASA_SERVER_URL = 'http://localhost:5005/webhooks/rest/webhook';
let conversationId = null;
let conversationHistory = [];

// Avatar states
const botAvatarStates = {
    idle: 'img/bot.jpeg',
    thinking: 'img/bot_thinking.jpeg',
    typing: 'img/bot_thinking.jpeg'
};

const userAvatarSrc = 'img/user.jpg';

// --- Event Listeners ---
sendButton.addEventListener('click', handleSendMessage);
userInput.addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
        handleSendMessage();
    }
});

// Add input animation
userInput.addEventListener('focus', function() {
    this.parentElement.classList.add('focused');
});

userInput.addEventListener('blur', function() {
    this.parentElement.classList.remove('focused');
});

// Add button animation
sendButton.addEventListener('mousedown', function() {
    this.classList.add('sending');
});

sendButton.addEventListener('mouseup', function() {
    this.classList.remove('sending');
});

// --- Functions ---
function setBotAvatarState(state) {
    if (headerBotAvatar && botAvatarStates[state]) {
        if (state === 'typing') {
            headerBotAvatar.classList.add('avatar-is-typing');
        } else {
            headerBotAvatar.classList.remove('avatar-is-typing');
        }
    } else {
        console.warn(`Avatar state '${state}' not defined or headerBotAvatar not found.`);
        if (headerBotAvatar) { 
            headerBotAvatar.classList.remove('avatar-is-typing');
        }
    }
}

function handleSendMessage() {
    const messageText = userInput.value.trim();
    if (messageText === '') return;

    // Animate send button
    sendButton.classList.add('sending');
    setTimeout(() => sendButton.classList.remove('sending'), 300);

    // Send the message
    sendMessage(messageText);
}

// Replace the existing sendMessage function with this one
async function sendMessage(messageText) {
    // Add user message to UI
    appendMessage(messageText, 'user');
    userInput.value = '';
    
    // Add to conversation history
    conversationHistory.push({sender: 'user', text: messageText});
    
    // Show typing indicator
    showTypingIndicator();
    setBotAvatarState('thinking');
    
    try {
        // Send message to Rasa
        const response = await fetch(RASA_SERVER_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sender: conversationId || `user_${Date.now()}`, // Use existing ID or create new one
                message: messageText
            })
        });
        
        const data = await response.json();
        
        // Store conversation ID if it's a new conversation
        if (!conversationId) {
            conversationId = `user_${Date.now()}`;
        }
        
        // Hide typing indicator
        hideTypingIndicator();
        
        // Process bot responses
        if (data && data.length > 0) {
            // Process each message from Rasa
            for (const message of data) {
                if (message.text) {
                    // Add to conversation history
                    conversationHistory.push({sender: 'bot', text: message.text});
                    
                    // Display with typing animation
                    setBotAvatarState('typing');
                    appendMessage(message.text, 'bot', true);
                    
                    // Calculate typing duration based on message length
                    const typingDuration = message.text.length * 50;
                    await new Promise(resolve => setTimeout(resolve, typingDuration));
                }
                
                // Handle images if your bot sends them
                if (message.image) {
                    appendImage(message.image, 'bot');
                }
                
                // Handle buttons if your bot sends them
                if (message.buttons && message.buttons.length > 0) {
                    appendButtons(message.buttons, 'bot');
                }
            }
            
            // Reset avatar state when done
            setBotAvatarState('idle');
        } else {
            // No response from bot
            appendMessage("I'm not sure how to respond to that.", 'bot', true);
        }
    } catch (error) {
        console.error('Error communicating with Rasa:', error);
        hideTypingIndicator();
        appendMessage("Sorry, I'm having trouble connecting right now.", 'bot');
        setBotAvatarState('idle');
    }
}

function appendMessage(text, sender, animateTyping = false) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.classList.add('message', sender);

    const avatarImg = document.createElement('img');
    avatarImg.classList.add('avatar');
    if (sender === 'user') {
        avatarImg.src = userAvatarSrc;
        avatarImg.alt = 'User Avatar';
    } else {
        avatarImg.src = botAvatarStates.idle;
        avatarImg.alt = 'Bot Avatar';
    }

    const messageContentDiv = document.createElement('div');
    messageContentDiv.classList.add('message-content');

    if (sender === 'bot' && animateTyping) {
        let i = 0;
        const speed = 15; // Changed from 50 to 20 milliseconds per character
        function typeWriter() {
            if (i < text.length) {
                messageContentDiv.textContent += text.charAt(i);
                i++;
                scrollToBottom(true);
                setTimeout(typeWriter, speed);
            }
        }
        typeWriter();
    } else {
        messageContentDiv.textContent = text;
    }

    // Structure: message -> [avatar, [content]] or [ [content], avatar]
    if (sender === 'user') {
        messageDiv.appendChild(messageContentDiv);
        messageDiv.appendChild(avatarImg);
    } else {
        messageDiv.appendChild(avatarImg);
        messageDiv.appendChild(messageContentDiv);
    }

    messagesContainer.appendChild(messageDiv);
    scrollToBottom();
}

// Add these helper functions for handling additional message types
function appendImage(imageUrl, sender) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.classList.add('message', sender);

    const avatarImg = document.createElement('img');
    avatarImg.classList.add('avatar');
    avatarImg.src = sender === 'user' ? userAvatarSrc : botAvatarStates.idle;
    avatarImg.alt = sender === 'user' ? 'User Avatar' : 'Bot Avatar';

    const messageContentDiv = document.createElement('div');
    messageContentDiv.classList.add('message-content');
    
    const image = document.createElement('img');
    image.src = imageUrl;
    image.alt = 'Bot sent image';
    image.classList.add('message-image');
    image.style.maxWidth = '100%';
    image.style.borderRadius = '8px';
    
    messageContentDiv.appendChild(image);

    if (sender === 'user') {
        messageDiv.appendChild(messageContentDiv);
        messageDiv.appendChild(avatarImg);
    } else {
        messageDiv.appendChild(avatarImg);
        messageDiv.appendChild(messageContentDiv);
    }

    messagesContainer.appendChild(messageDiv);
    scrollToBottom();
}

function appendButtons(buttons, sender) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.classList.add('message', sender);

    const avatarImg = document.createElement('img');
    avatarImg.classList.add('avatar');
    avatarImg.src = botAvatarStates.idle;
    avatarImg.alt = 'Bot Avatar';

    const messageContentDiv = document.createElement('div');
    messageContentDiv.classList.add('message-content');
    
    const buttonsContainer = document.createElement('div');
    buttonsContainer.classList.add('buttons-container');
    buttonsContainer.style.display = 'flex';
    buttonsContainer.style.flexDirection = 'column';
    buttonsContainer.style.gap = '8px';
    buttonsContainer.style.marginTop = '8px';
    
    buttons.forEach(button => {
        const buttonElement = document.createElement('button');
        buttonElement.textContent = button.title;
        buttonElement.classList.add('message-button');
        buttonElement.style.padding = '8px 16px';
        buttonElement.style.backgroundColor = 'var(--primary-light)';
        buttonElement.style.color = 'white';
        buttonElement.style.border = 'none';
        buttonElement.style.borderRadius = '16px';
        buttonElement.style.cursor = 'pointer';
        buttonElement.style.fontSize = '0.9rem';
        buttonElement.style.fontFamily = 'inherit';
        buttonElement.style.transition = 'all 0.2s ease';
        
        buttonElement.addEventListener('mouseover', () => {
            buttonElement.style.backgroundColor = 'var(--primary-dark)';
        });
        
        buttonElement.addEventListener('mouseout', () => {
            buttonElement.style.backgroundColor = 'var(--primary-light)';
        });
        
        buttonElement.addEventListener('click', () => {
            // When button is clicked, send its payload to Rasa
            sendMessage(button.payload);
        });
        
        buttonsContainer.appendChild(buttonElement);
    });
    
    messageContentDiv.appendChild(buttonsContainer);
    messageDiv.appendChild(avatarImg);
    messageDiv.appendChild(messageContentDiv);

    messagesContainer.appendChild(messageDiv);
    scrollToBottom();
}

function showTypingIndicator() {
    const messagesContainer = document.getElementById('chat-messages');
    let typingIndicatorDiv = document.getElementById('typing-indicator-message');

    if (!typingIndicatorDiv) {
        typingIndicatorDiv = document.createElement('div');
        typingIndicatorDiv.classList.add('message', 'bot', 'typing-indicator-message');
        typingIndicatorDiv.id = 'typing-indicator-message';

        const avatarImg = document.createElement('img');
        avatarImg.classList.add('avatar');
        avatarImg.src = botAvatarStates.thinking;
        avatarImg.alt = 'Bot Typing';

        const messageContentDiv = document.createElement('div');
        messageContentDiv.classList.add('message-content');
        
        // Create animated dots
        const textSpan = document.createElement('span');
        textSpan.textContent = 'Bot is typing';
        
        const dotsContainer = document.createElement('div');
        dotsContainer.classList.add('typing-dots');
        
        for (let i = 0; i < 3; i++) {
            const dot = document.createElement('span');
            dot.classList.add('typing-dot');
            dotsContainer.appendChild(dot);
        }
        
        messageContentDiv.appendChild(textSpan);
        messageContentDiv.appendChild(dotsContainer);

        typingIndicatorDiv.appendChild(avatarImg);
        typingIndicatorDiv.appendChild(messageContentDiv);
        messagesContainer.appendChild(typingIndicatorDiv);
    }

    typingIndicatorDiv.style.display = 'flex';
    scrollToBottom();
    setBotAvatarState('thinking');
}

function hideTypingIndicator() {
    const typingIndicatorDiv = document.getElementById('typing-indicator-message');
    if (typingIndicatorDiv) {
        // Add fade-out animation
        typingIndicatorDiv.style.opacity = '0';
        typingIndicatorDiv.style.transform = 'translateY(10px)';
        
        // Remove after animation completes
        setTimeout(() => {
            if (typingIndicatorDiv.parentNode) {
                typingIndicatorDiv.parentNode.removeChild(typingIndicatorDiv);
            }
        }, 300);
    }
    setBotAvatarState('idle');
}

function scrollToBottom(smooth = false) {
    const messagesContainer = document.getElementById('chat-messages');
    if (smooth) {
        messagesContainer.scrollTo({
            top: messagesContainer.scrollHeight,
            behavior: 'smooth'
        });
    } else {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
}

// Modify the initialBotGreeting function to use Rasa
async function initialBotGreeting() {
    showTypingIndicator();
    setBotAvatarState('thinking');
    
    try {
        // Send an initial message to Rasa to start the conversation
        const response = await fetch(RASA_SERVER_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sender: `user_${Date.now()}`,
                message: '/start'
            })
        });
        
        const data = await response.json();
        
        // Store conversation ID
        conversationId = `user_${Date.now()}`;
        
        hideTypingIndicator();
        
        // Display welcome message from Rasa or use default
        if (data && data.length > 0 && data[0].text) {
            appendMessage(data[0].text, 'bot', true);
            conversationHistory.push({sender: 'bot', text: data[0].text});
        } else {
            const initialMsg = "Hello! I am ARHAM, your physiotherapy assistant. How can I help you today?";
            appendMessage(initialMsg, 'bot', true);
            conversationHistory.push({sender: 'bot', text: initialMsg});
        }
        
    } catch (error) {
        console.error('Error communicating with Rasa:', error);
        hideTypingIndicator();
        const initialMsg = "Hello! I am ARHAM, your physiotherapy assistant. How can I help you today?";
        appendMessage(initialMsg, 'bot', true);
        conversationHistory.push({sender: 'bot', text: initialMsg});
    }
    
    setBotAvatarState('idle');
}

window.onload = () => {
    // Set initial CSS state for header avatar
    setBotAvatarState('idle');
    
    // Add a slight delay before greeting for a smoother startup experience
    setTimeout(() => {
        initialBotGreeting();
        userInput.focus();
    }, 500);
};
