:root {
    --primary-color: #4a6fa5; /* Calm blue */
    --primary-light: #6b8cb8;
    --primary-dark: #345888;
    --accent-color: #ff7e5f; /* Warm coral */
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #ffffff;
    --bg-color: #f8f9fb;
    --container-bg: #ffffff;
    --user-msg-bg: #4a6fa5;
    --bot-msg-bg: #f0f2f5;
    --input-bg: #f5f7fa;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 5px 15px rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Poppins', 'Roboto', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    line-height: 1.5;
}

/* Main Container */
.chat-container {
    width: 90%;
    max-width: 420px;
    height: 85vh;
    max-height: 700px;
    background-color: var(--container-bg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-medium);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    transition: all var(--transition-speed) ease;
}

/* Header Styles */
.chat-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-light);
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-info {
    display: flex;
    flex-direction: column;
}

.chat-header h1 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.75rem;
    opacity: 0.9;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #4CAF50;
    display: inline-block;
}

.status-dot.online {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

.header-controls {
    display: flex;
    gap: 8px;
}

.header-button {
    background: none;
    border: none;
    color: white;
    opacity: 0.7;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.header-button:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
}

.header-button svg {
    fill: currentColor;
}

/* Avatar Styles */
.avatar {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.bot-avatar-header {
    width: 42px;
    height: 42px;
}

/* Messages Area */
.chat-messages {
    flex-grow: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

/* Message Styles */
.message {
    display: flex;
    max-width: 85%;
    opacity: 0;
    transform: translateY(20px);
    animation: messageFadeIn 0.5s forwards;
    position: relative;
}

@keyframes messageFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-content {
    padding: 12px 16px;
    border-radius: var(--border-radius-lg);
    line-height: 1.4;
    font-size: 0.95rem;
    box-shadow: var(--shadow-light);
    transition: transform 0.2s ease;
}

/* User Message Styles */
.message.user {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message.user .message-content {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-light);
    border-bottom-right-radius: 4px;
    margin-right: 8px;
}

.message.user .message-content::after {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 12px;
    height: 12px;
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    border-bottom-right-radius: 12px;
    z-index: -1;
}

/* Bot Message Styles */
.message.bot {
    align-self: flex-start;
}

.message.bot .message-content {
    background-color: var(--bot-msg-bg);
    color: var(--text-primary);
    border-bottom-left-radius: 4px;
    margin-left: 8px;
}

.message.bot .message-content::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 12px;
    height: 12px;
    background: var(--bot-msg-bg);
    border-bottom-left-radius: 12px;
    z-index: -1;
}

/* Message Hover Effect */
.message:hover .message-content {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.message .avatar {
    align-self: flex-end;
}

/* Typing Indicator */
.typing-indicator-message {
    align-self: flex-start;
}

.typing-indicator-message .message-content {
    background-color: var(--bot-msg-bg);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    padding: 12px 16px;
    font-style: normal;
}

.typing-dots {
    display: flex;
    gap: 4px;
    margin-left: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background-color: var(--text-secondary);
    border-radius: 50%;
    opacity: 0.6;
    animation: typingAnimation 1.4s infinite both;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingAnimation {
    0%, 100% { transform: translateY(0); opacity: 0.6; }
    50% { transform: translateY(-4px); opacity: 1; }
}

/* Input Area */
.chat-input-area {
    padding: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    background-color: var(--container-bg);
    position: relative;
}

.input-container {
    display: flex;
    align-items: center;
    background-color: var(--input-bg);
    border-radius: var(--border-radius-lg);
    padding: 4px 8px 4px 16px;
    box-shadow: var(--shadow-light);
    transition: box-shadow 0.3s ease;
}

.input-container:focus-within {
    box-shadow: 0 0 0 2px rgba(74, 111, 165, 0.3);
}

#user-input {
    flex-grow: 1;
    border: none;
    background: transparent;
    padding: 12px 0;
    font-size: 0.95rem;
    color: var(--text-primary);
    font-family: inherit;
    outline: none;
}

#user-input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.send-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 8px;
}

.send-button:hover {
    background-color: var(--primary-dark);
    transform: scale(1.05);
}

.send-button:active {
    transform: scale(0.95);
}

.send-button svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

/* Animation for send button */
.send-button.sending {
    animation: sendPulse 0.5s ease;
}

@keyframes sendPulse {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .chat-container {
        width: 100%;
        height: 100vh;
        max-height: none;
        border-radius: 0;
    }
    
    body {
        padding: 0;
    }
    
    .chat-header h1 {
        font-size: 1.1rem;
    }
    
    .message {
        max-width: 90%;
    }
    
    .avatar {
        width: 34px;
        height: 34px;
    }
}

@media (max-width: 480px) {
    .chat-header {
        padding: 12px 16px;
    }
    
    .chat-messages {
        padding: 16px;
    }
    
    .message-content {
        padding: 10px 14px;
        font-size: 0.9rem;
    }
    
    .avatar {
        width: 30px;
        height: 30px;
    }
    
    .bot-avatar-header {
        width: 36px;
        height: 36px;
    }
}