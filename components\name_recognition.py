"""
Custom component for name recognition using AI4Bharat's IndicNER and spaCy models.
This component is context-aware and only activates when the bot is asking for a name.
"""

import logging
import os
from typing import Any, Dict, List, Text, Optional

from rasa.engine.graph import ExecutionContext
from rasa.engine.recipes.default_recipe import DefaultV1Recipe
from rasa.engine.storage.resource import Resource
from rasa.engine.storage.storage import ModelStorage
from rasa.nlu.extractors.extractor import EntityExtractorMixin
from rasa.shared.nlu.training_data.message import Message
from rasa.shared.nlu.constants import (
    ENTITIES,
    ENTITY_ATTRIBUTE_TYPE,
    ENTITY_ATTRIBUTE_START,
    ENTITY_ATTRIBUTE_END,
    ENTITY_ATTRIBUTE_VALUE,
    TEXT,
    INTENT,
)

# Import transformers for AI4Bharat's IndicNER
try:
    from transformers import AutoTokenizer, AutoModelForTokenClassification, pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logging.warning("'transformers' library not found. Please install it with: pip install transformers")

# Import spaCy as a secondary NER model
try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    logging.warning("'spacy' library not found. Please install it with: pip install spacy")

# Configuration
INDIC_NER_WEIGHT = 0.7  # Weight for IndicNER model in ensemble
SPACY_WEIGHT = 0.3  # Weight for spaCy model in ensemble

# List of ambiguous words that can be both names and common nouns
AMBIGUOUS_NAMES = [
    "honey", "rose", "joy", "faith", "hope", "grace", "sky", 
    "sunny", "autumn", "summer", "spring", "winter", "amber", "crystal",
    "diamond", "ruby", "pearl", "jade", "jasmine", "lily", "daisy",
    "star", "moon", "dawn", "dusk", "rain", "cloud", "river", "ocean"
]

# List of intents and actions that indicate we're asking for a name
NAME_REQUEST_INDICATORS = [
    "utter_ask_name",  # Bot action asking for name
]

logger = logging.getLogger(__name__)

@DefaultV1Recipe.register(
    [DefaultV1Recipe.ComponentType.ENTITY_EXTRACTOR], is_trainable=False
)
class ContextAwareNameRecognition(EntityExtractorMixin):
    """
    Custom entity extractor that combines AI4Bharat's IndicNER and spaCy models
    for improved name recognition. Only activates when the bot is asking for a name.
    """

    def __init__(
        self,
        config: Dict[Text, Any],
        model_storage: ModelStorage,
        resource: Resource,
        execution_context: ExecutionContext,
    ) -> None:
        """Initialize the name recognition component."""
        self._config = config
        self.indic_ner_pipeline = None
        self.spacy_nlp = None
        
        # Initialize models
        self._initialize_models()

    def _initialize_models(self) -> None:
        """Initialize the AI4Bharat IndicNER and spaCy models."""
        # Initialize IndicNER model
        if TRANSFORMERS_AVAILABLE:
            try:
                model_name = "ai4bharat/IndicNER"
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                model = AutoModelForTokenClassification.from_pretrained(model_name)
                # Using aggregation_strategy="simple" to group subword tokens for entities
                self.indic_ner_pipeline = pipeline("ner", model=model, tokenizer=tokenizer, aggregation_strategy="simple")
                logger.info(f"Successfully loaded Hugging Face model '{model_name}'.")
            except Exception as e:
                logger.error(f"Error initializing IndicNER model: {e}")
                self.indic_ner_pipeline = None
        
        # Initialize spaCy model
        if SPACY_AVAILABLE:
            try:
                # Try to load the English model with the largest vocabulary first
                try:
                    self.spacy_nlp = spacy.load("en_core_web_lg")
                    logger.info("Successfully loaded spaCy model 'en_core_web_lg'.")
                except:
                    try:
                        self.spacy_nlp = spacy.load("en_core_web_md")
                        logger.info("Successfully loaded spaCy model 'en_core_web_md'.")
                    except:
                        self.spacy_nlp = spacy.load("en_core_web_sm")
                        logger.info("Successfully loaded spaCy model 'en_core_web_sm'.")
            except Exception as e:
                logger.error(f"Error initializing spaCy model: {e}")
                self.spacy_nlp = None

    def _is_name_context(self, message: Message) -> bool:
        """
        Check if the current context is asking for a name.
        This is determined by looking at the previous bot action.
        """
        # Get the latest action from the tracker
        tracker = message.get("tracker", {})
        latest_action = tracker.get("latest_action_name", "")
        
        # Check if the latest action is asking for a name
        is_name_context = latest_action in NAME_REQUEST_INDICATORS
        
        logger.debug(f"Context check: latest_action={latest_action}, is_name_context={is_name_context}")
        return is_name_context

    def _extract_names_with_spacy(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract person names using spaCy NER.
        
        Args:
            text: The input text to analyze
            
        Returns:
            List of dictionaries containing entity information
        """
        if not self.spacy_nlp:
            return []
            
        try:
            # Process the text with spaCy
            doc = self.spacy_nlp(text)
            
            # Extract person entities
            person_entities = []
            for ent in doc.ents:
                if ent.label_ == "PERSON":
                    person_entities.append({
                        "entity_group": "PER",  # Use same format as IndicNER for consistency
                        "word": ent.text,
                        "start": ent.start_char,
                        "end": ent.end_char,
                        "score": 0.85,  # spaCy doesn't provide confidence scores, so we use a default
                        "model": "spacy"
                    })
            
            return person_entities
        except Exception as e:
            logger.error(f"Error extracting names with spaCy: {e}")
            return []
    
    def _extract_names_with_indic_ner(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract person names using AI4Bharat's IndicNER.
        
        Args:
            text: The input text to analyze
            
        Returns:
            List of dictionaries containing entity information
        """
        if not self.indic_ner_pipeline:
            return []
            
        try:
            # Use the Hugging Face NER pipeline
            ner_results = self.indic_ner_pipeline(text)
            
            # Filter for person entities
            person_entities = []
            for entity in ner_results:
                if entity['entity_group'] == 'PER':
                    # Convert numpy float32 to Python float to ensure JSON serialization works
                    entity['score'] = float(entity['score'])
                    entity['model'] = 'indic_ner'
                    person_entities.append(entity)
            
            return person_entities
        except Exception as e:
            logger.error(f"Error extracting names with IndicNER: {e}")
            return []
    
    def _combine_entity_results(self, indic_entities: List[Dict[str, Any]], spacy_entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Combine entity results from multiple models using a weighted ensemble approach.
        
        Args:
            indic_entities: Entities from IndicNER
            spacy_entities: Entities from spaCy
            
        Returns:
            Dictionary with the highest confidence entity and its metadata
        """
        all_entities = indic_entities + spacy_entities
        
        if not all_entities:
            return None
            
        # Group entities by name (case-insensitive)
        entity_groups = {}
        for entity in all_entities:
            name = entity['word'].lower()
            if name not in entity_groups:
                entity_groups[name] = []
            entity_groups[name].append(entity)
        
        # Calculate combined confidence for each name
        best_entity = None
        highest_confidence = 0.0
        
        for name, entities in entity_groups.items():
            # Calculate weighted confidence based on which models detected this name
            combined_confidence = 0.0
            indic_detected = False
            spacy_detected = False
            
            # Get the highest confidence entity from each model
            best_indic_entity = None
            best_spacy_entity = None
            
            for entity in entities:
                if entity['model'] == 'indic_ner':
                    indic_detected = True
                    if not best_indic_entity or entity['score'] > best_indic_entity['score']:
                        best_indic_entity = entity
                elif entity['model'] == 'spacy':
                    spacy_detected = True
                    if not best_spacy_entity or entity['score'] > best_spacy_entity['score']:
                        best_spacy_entity = entity
            
            # Calculate weighted confidence
            if indic_detected and spacy_detected:
                # Both models detected this name - use weighted average
                indic_confidence = best_indic_entity['score']
                spacy_confidence = best_spacy_entity['score']
                combined_confidence = (indic_confidence * INDIC_NER_WEIGHT) + (spacy_confidence * SPACY_WEIGHT)
                
                # Use the entity with better positioning (start/end) information
                selected_entity = best_indic_entity  # Prefer IndicNER for positioning
            elif indic_detected:
                # Only IndicNER detected this name
                combined_confidence = best_indic_entity['score'] * 0.9  # Slight penalty for single model
                selected_entity = best_indic_entity
            elif spacy_detected:
                # Only spaCy detected this name
                combined_confidence = best_spacy_entity['score'] * 0.9  # Slight penalty for single model
                selected_entity = best_spacy_entity
            
            # Update the best entity if this one has higher confidence
            if combined_confidence > highest_confidence:
                highest_confidence = combined_confidence
                
                # Create a new entity with the combined confidence
                best_entity = selected_entity.copy()
                best_entity['score'] = combined_confidence
                best_entity['models_agreed'] = indic_detected and spacy_detected
            
        return best_entity
    
    def _is_ambiguous_name(self, name: str) -> bool:
        """
        Check if a name is in our list of ambiguous names.
        
        Args:
            name: The name to check
            
        Returns:
            bool: True if the name is in our ambiguous names list
        """
        return name.lower() in AMBIGUOUS_NAMES

    def process(self, message: Message, **kwargs: Any) -> None:
        """
        Process the message and extract name entities if in a name-asking context.
        
        Args:
            message: The message to process
            **kwargs: Additional arguments
        """
        # Check if we're in a context where we should be looking for names
        is_name_context = self._is_name_context(message)
        
        # Only process if we're in a name context or if this is a fallback case
        # (when the form validation is asking for a name)
        if not is_name_context and "name" not in message.get_intent().lower():
            logger.debug("Not in name context, skipping name recognition")
            return
        
        # Get the text from the message
        text = message.get(TEXT)
        if not text:
            return
        
        # Extract names using both models
        indic_entities = self._extract_names_with_indic_ner(text)
        spacy_entities = self._extract_names_with_spacy(text)
        
        # Get the best entity using our ensemble approach
        best_entity = self._combine_entity_results(indic_entities, spacy_entities)
        
        # If we found a name entity
        if best_entity:
            # Extract the name and confidence
            name = best_entity['word']
            confidence = best_entity['score']
            models_agreed = best_entity.get('models_agreed', False)
            is_ambiguous = self._is_ambiguous_name(name)
            
            # Determine if we need confirmation based on confidence and other factors
            needs_confirmation = True
            
            # High confidence and both models agree - no confirmation needed
            if models_agreed and confidence >= 0.9:
                needs_confirmation = False
            # High confidence but only one model - still pretty confident
            elif confidence >= 0.95:
                needs_confirmation = False
            # Medium-high confidence and not ambiguous - moderately confident
            elif confidence >= 0.85 and not is_ambiguous:
                needs_confirmation = False
                
            # Create the entity
            entity = {
                ENTITY_ATTRIBUTE_TYPE: "name",
                ENTITY_ATTRIBUTE_START: best_entity['start'],
                ENTITY_ATTRIBUTE_END: best_entity['end'],
                ENTITY_ATTRIBUTE_VALUE: name,
                "confidence": confidence,
                "extractor": "context_aware_name_recognition"
            }
            
            # Add the entity to the message
            message.set(
                ENTITIES,
                message.get(ENTITIES, []) + [entity],
                add_to_output=True
            )
            
            # Add metadata for the name validation action
            name_confidence_metadata = {
                "name_confidence": confidence,
                "name_needs_confirmation": needs_confirmation,
                "name_candidate": name,
                "is_ambiguous_name": is_ambiguous,
                "is_name_context": is_name_context,
                "is_fallback_detection": not is_name_context,  # True if we're not in a name context but still detected a name
                "models_agreed": models_agreed
            }
            
            # Set the metadata on the message
            message.set("name_confidence_metadata", name_confidence_metadata)
            
            logger.debug(f"Extracted name: {name} (confidence: {confidence}, needs_confirmation: {needs_confirmation})")
