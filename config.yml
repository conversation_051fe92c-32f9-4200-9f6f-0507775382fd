# config.yml
recipe: default.v1
language: en

pipeline:
  # Featurizers
  - name: WhitespaceTokenizer
  - name: RegexFeaturizer
  - name: LexicalSyntacticFeaturizer
  - name: CountVectorsFeaturizer
    analyzer: word
    min_ngram: 1
    max_ngram: 1
  - name: CountVectorsFeaturizer
    analyzer: char_wb
    min_ngram: 1
    max_ngram: 4

  # NLU Components
  - name: DIETClassifier
    epochs: 100
    constrain_resources: true
  - name: EntitySynonymMapper
  - name: custom_ner_components.IndicNERFallback # Custom component for Indic NER
    confidence_threshold: 0.75  # Names with confidence above this are accepted without confirmation
    low_confidence_threshold: 0.5  # Names with confidence below this are ignored
    # Ensemble configuration
    use_ensemble: true  # Whether to use the ensemble approach with multiple models
    indic_ner_weight: 0.7  # Weight for IndicNER model in ensemble
    spacy_weight: 0.3  # Weight for spaCy model in ensemble
    # List of words that can be both names and common nouns - these will always trigger confirmation
    ambiguous_names:
      - honey
      - rose
      - joy
      - faith
      - hope
      - grace
      - sky
      - sunny
      - amber
      - crystal
      - ruby
      - pearl
      - jade
      - jasmine
      - lily
      - daisy
      - star
      - moon
      - dawn
      - dusk
      - rain
      - cloud
      - river
      - ocean
  - name: ResponseSelector
    epochs: 100
    constrain_resources: true


policies:
  - name: MemoizationPolicy
  - name: TEDPolicy
    max_history: 5
    epochs: 100
  - name: RulePolicy

# The assistant_id is preserved from your original config
assistant_id: 20250501-151818-swift-fortress