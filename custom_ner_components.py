from typing import Any, Text, Dict, List, Type, Union, Optional, Tuple
import numpy as np
import logging
import re

from rasa.engine.graph import GraphComponent, ExecutionContext
from rasa.engine.recipes.default_recipe import DefaultV1Recipe
from rasa.engine.storage.resource import Resource
from rasa.engine.storage.storage import ModelStorage
from rasa.shared.nlu.training_data.message import Message
from rasa.shared.nlu.training_data.training_data import TrainingData

logger = logging.getLogger(__name__)

# Import transformers for AI4Bharat's IndicNER
try:
    from transformers import AutoTokenizer, AutoModelForTokenClassification, pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    AutoTokenizer, AutoModelForTokenClassification, pipeline = None, None, None

# Import spaCy as a secondary NER model
try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    spacy = None

@DefaultV1Recipe.register(
    [DefaultV1Recipe.ComponentType.ENTITY_EXTRACTOR], is_trainable=False
)
class IndicNERFallback(GraphComponent):
    """A custom NLU component to use ai-bharat-indicner as a fallback for person name recognition."""

    @classmethod
    def create(
        cls,
        config: Dict[Text, Any],
        model_storage: ModelStorage,
        resource: Resource,
        execution_context: ExecutionContext,
    ) -> "IndicNERFallback":
        return cls(config)



    def __init__(self, config: Dict[Text, Any]) -> None:
        # Initialize NER models
        self.indic_ner_pipeline = None
        self.spacy_nlp = None

        # Default confidence threshold - can be configured in config.yml
        self.confidence_threshold = config.get("confidence_threshold", 0.75)
        self.low_confidence_threshold = config.get("low_confidence_threshold", 0.5)

        # Ensemble configuration
        self.use_ensemble = config.get("use_ensemble", True)
        self.indic_ner_weight = config.get("indic_ner_weight", 0.7)  # Weight for IndicNER model
        self.spacy_weight = config.get("spacy_weight", 0.3)  # Weight for spaCy model

        # List of ambiguous words that can be both names and common nouns
        # These will always trigger confirmation regardless of confidence
        self.ambiguous_names = config.get("ambiguous_names", [
            "honey", "rose", "joy", "faith", "hope", "grace", "sky",
            "sunny", "autumn", "summer", "spring", "winter", "amber", "crystal",
            "diamond", "ruby", "pearl", "jade", "jasmine", "lily", "daisy",
            "star", "moon", "dawn", "dusk", "rain", "cloud", "river", "ocean"
        ])

        # Convert to lowercase for case-insensitive matching
        self.ambiguous_names = [name.lower() for name in self.ambiguous_names]

        # Name question patterns to detect when the bot is explicitly asking for a name
        self.name_question_patterns = [
            r"what is your name",
            r"could you please tell me your name",
            r"may i know your name",
            r"please enter your full name",
            r"please tell me your name",
            r"what's your name",
            r"who am i speaking with",
            r"who are you",
            r"your name please",
            r"can i have your name",
            r"may i have your name",
            r"name please",
            r"please provide your name",
            r"what should i call you",
            r"how should i address you",
            r"what name would you like to use",
            r"under what name",
            r"what name would you prefer",
            r"please share your name",
            r"i need your name",
            r"i'll need your name",
            r"i will need your name",
            r"could i get your name",
            r"would you mind telling me your name",
            r"would you share your name",
            r"please enter name"
        ]

        logger.info(f"IndicNERFallback initialized with confidence_threshold={self.confidence_threshold}, "
                   f"low_confidence_threshold={self.low_confidence_threshold}, "
                   f"ambiguous_names_count={len(self.ambiguous_names)}, "
                   f"use_ensemble={self.use_ensemble}")

        # Initialize IndicNER model
        if TRANSFORMERS_AVAILABLE:
            try:
                model_name = "ai4bharat/IndicNER"
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                model = AutoModelForTokenClassification.from_pretrained(model_name)
                # Using aggregation_strategy="simple" to group subword tokens for entities
                self.indic_ner_pipeline = pipeline("ner", model=model, tokenizer=tokenizer, aggregation_strategy="simple")
                logger.info(f"IndicNERFallback: Successfully loaded Hugging Face model '{model_name}'.")
            except Exception as e:
                logger.error(f"Error initializing IndicNERFallback with Hugging Face model: {e}")
                self.indic_ner_pipeline = None
        else:
            logger.warning("'transformers' library not found. IndicNER model will not be available.")

        # Initialize spaCy model
        if SPACY_AVAILABLE:
            try:
                # Try to load the English model with the largest vocabulary
                # This model has better NER capabilities
                self.spacy_nlp = spacy.load("en_core_web_lg")
                logger.info("IndicNERFallback: Successfully loaded spaCy model 'en_core_web_lg'.")
            except Exception as e:
                try:
                    # Fall back to the medium model if the large one is not available
                    self.spacy_nlp = spacy.load("en_core_web_md")
                    logger.info("IndicNERFallback: Successfully loaded spaCy model 'en_core_web_md'.")
                except Exception as e2:
                    try:
                        # Fall back to the small model as a last resort
                        self.spacy_nlp = spacy.load("en_core_web_sm")
                        logger.info("IndicNERFallback: Successfully loaded spaCy model 'en_core_web_sm'.")
                    except Exception as e3:
                        logger.error(f"Error initializing spaCy model: {e3}")
                        self.spacy_nlp = None
        else:
            logger.warning("'spacy' library not found. spaCy model will not be available.")

    def _is_name_asking_context(self, message: Message) -> bool:
        """
        Determine if the current context is asking for a name.
        This checks if the previous bot message was asking for a name.

        Args:
            message: The current message

        Returns:
            bool: True if the context suggests we're asking for a name
        """
        # Check if we're in a form that's requesting the name slot
        active_loop = message.get("active_loop", {}).get("name")
        requested_slot = message.get("requested_slot")

        if active_loop and requested_slot == "name":
            logger.debug("Name-asking context detected: form is requesting name slot")
            return True

        # Check if the latest action was asking for a name
        latest_action = message.get("latest_action_name", "")
        if latest_action in ["utter_ask_name", "action_ask_name", "utter_ask_appointment_form_name"]:
            logger.debug(f"Name-asking context detected: latest action was {latest_action}")
            return True

        # Check if the last bot message matches any name question patterns
        last_bot_message = message.get("latest_bot_message", {}).get("text", "")
        if last_bot_message:
            last_bot_message_lower = last_bot_message.lower()
            for pattern in self.name_question_patterns:
                if re.search(pattern, last_bot_message_lower):
                    logger.debug(f"Name-asking context detected: bot asked '{last_bot_message}'")
                    return True

        # Check if the message intent is explicitly about providing a name
        intent = message.get("intent", {}).get("name", "")
        if intent and ("provide_name" in intent or "inform" in intent):
            # Check if this is a direct response to a name request
            # This helps with fallback cases where the context might be lost
            logger.debug(f"Name-providing intent detected: {intent}")
            return True

        return False

    def _is_ambiguous_name(self, name: str) -> bool:
        """
        Check if a name is in our list of ambiguous names.

        Args:
            name: The name to check

        Returns:
            bool: True if the name is in our ambiguous names list
        """
        return name.lower() in self.ambiguous_names

    def _extract_names_with_spacy(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract person names using spaCy NER.

        Args:
            text: The input text to analyze

        Returns:
            List of dictionaries containing entity information
        """
        if not self.spacy_nlp:
            return []

        try:
            # Process the text with spaCy
            doc = self.spacy_nlp(text)

            # Extract person entities
            person_entities = []
            for ent in doc.ents:
                if ent.label_ == "PERSON":
                    person_entities.append({
                        "entity_group": "PER",  # Use same format as IndicNER for consistency
                        "word": ent.text,
                        "start": ent.start_char,
                        "end": ent.end_char,
                        "score": 0.85,  # spaCy doesn't provide confidence scores, so we use a default
                        "model": "spacy"
                    })

            return person_entities
        except Exception as e:
            logger.error(f"Error extracting names with spaCy: {e}")
            return []

    def _extract_names_with_indic_ner(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract person names using AI4Bharat's IndicNER.

        Args:
            text: The input text to analyze

        Returns:
            List of dictionaries containing entity information
        """
        if not self.indic_ner_pipeline:
            return []

        try:
            # Use the Hugging Face NER pipeline
            ner_results = self.indic_ner_pipeline(text)

            # Filter for person entities
            person_entities = []
            for entity in ner_results:
                if entity['entity_group'] == 'PER':
                    # Convert numpy float32 to Python float to ensure JSON serialization works
                    entity['score'] = float(entity['score'])
                    entity['model'] = 'indic_ner'
                    person_entities.append(entity)

            return person_entities
        except Exception as e:
            logger.error(f"Error extracting names with IndicNER: {e}")
            return []

    def _combine_entity_results(self, indic_entities: List[Dict[str, Any]], spacy_entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Combine entity results from multiple models using a weighted ensemble approach.

        Args:
            indic_entities: Entities from IndicNER
            spacy_entities: Entities from spaCy

        Returns:
            Dictionary with the highest confidence entity and its metadata
        """
        all_entities = indic_entities + spacy_entities

        if not all_entities:
            return None

        # Group entities by name (case-insensitive)
        entity_groups = {}
        for entity in all_entities:
            name = entity['word'].lower()
            if name not in entity_groups:
                entity_groups[name] = []
            entity_groups[name].append(entity)

        # Calculate combined confidence for each name
        best_entity = None
        highest_confidence = 0.0

        for name, entities in entity_groups.items():
            # Calculate weighted confidence based on which models detected this name
            combined_confidence = 0.0
            indic_detected = False
            spacy_detected = False

            # Get the highest confidence entity from each model
            best_indic_entity = None
            best_spacy_entity = None

            for entity in entities:
                if entity['model'] == 'indic_ner':
                    indic_detected = True
                    if not best_indic_entity or entity['score'] > best_indic_entity['score']:
                        best_indic_entity = entity
                elif entity['model'] == 'spacy':
                    spacy_detected = True
                    if not best_spacy_entity or entity['score'] > best_spacy_entity['score']:
                        best_spacy_entity = entity

            # Calculate weighted confidence
            if indic_detected and spacy_detected:
                # Both models detected this name - use weighted average
                indic_confidence = best_indic_entity['score']
                spacy_confidence = best_spacy_entity['score']
                combined_confidence = (indic_confidence * self.indic_ner_weight) + (spacy_confidence * self.spacy_weight)

                # Use the entity with better positioning (start/end) information
                selected_entity = best_indic_entity  # Prefer IndicNER for positioning
            elif indic_detected:
                # Only IndicNER detected this name
                combined_confidence = best_indic_entity['score'] * 0.9  # Slight penalty for single model
                selected_entity = best_indic_entity
            elif spacy_detected:
                # Only spaCy detected this name
                combined_confidence = best_spacy_entity['score'] * 0.9  # Slight penalty for single model
                selected_entity = best_spacy_entity

            # Update the best entity if this one has higher confidence
            if combined_confidence > highest_confidence:
                highest_confidence = combined_confidence

                # Create a new entity with the combined confidence
                best_entity = selected_entity.copy()
                best_entity['score'] = combined_confidence
                best_entity['models_agreed'] = indic_detected and spacy_detected

        return best_entity

    def process(self, messages: List[Message]) -> List[Message]:
        """Process a list of messages and extract entities if 'name' is not already found."""

        # Check if we have at least one NER model available
        if not self.indic_ner_pipeline and not self.spacy_nlp:
            # Silently pass if no NER models are available
            logger.warning("No NER models available. IndicNERFallback will not function.")
            return messages

        for message in messages:
            existing_entities = message.get("entities", [])
            has_name_entity = any(entity.get("entity") == "name" for entity in existing_entities)

            if has_name_entity:
                # If a 'name' entity is already extracted by other components, do nothing.
                continue

            text = message.get("text")
            if not text:
                continue

            # Check if we're in a name-asking context - THIS IS THE KEY CHECK
            is_name_context = self._is_name_asking_context(message)

            # Only proceed with name extraction if we're in a name-asking context
            # This ensures name recognition only happens when the bot is asking for a name
            if not is_name_context:
                logger.debug("Not in name-asking context, skipping name recognition")
                continue

            extracted_entities = []

            try:
                # Extract names using both models
                indic_entities = self._extract_names_with_indic_ner(text)
                spacy_entities = self._extract_names_with_spacy(text)

                logger.debug(f"IndicNER found {len(indic_entities)} entities, spaCy found {len(spacy_entities)} entities")

                # Get the best entity using our ensemble approach
                highest_confidence_entity = None

                if self.use_ensemble and self.indic_ner_pipeline and self.spacy_nlp:
                    # Use ensemble approach if both models are available
                    highest_confidence_entity = self._combine_entity_results(indic_entities, spacy_entities)
                    if highest_confidence_entity:
                        logger.debug(f"Ensemble approach selected entity: {highest_confidence_entity['word']} with confidence {highest_confidence_entity['score']}")
                elif indic_entities:
                    # Fall back to IndicNER if ensemble is not available
                    highest_confidence = 0.0
                    for entity in indic_entities:
                        if entity['score'] > highest_confidence:
                            highest_confidence = entity['score']
                            highest_confidence_entity = entity
                elif spacy_entities:
                    # Fall back to spaCy if IndicNER is not available
                    highest_confidence = 0.0
                    for entity in spacy_entities:
                        if entity['score'] > highest_confidence:
                            highest_confidence = entity['score']
                            highest_confidence_entity = entity

                # Process the highest confidence entity if found
                if highest_confidence_entity:
                    confidence = float(highest_confidence_entity['score'])
                    name_value = highest_confidence_entity['word']
                    models_agreed = highest_confidence_entity.get('models_agreed', False)

                    # Check if the name is in our ambiguous list
                    is_ambiguous = self._is_ambiguous_name(name_value)

                    # Boost confidence if we're in a name-asking context
                    context_adjusted_confidence = confidence
                    # Boost confidence by 15% if we're explicitly asking for a name
                    context_adjusted_confidence = min(confidence * 1.15, 1.0)
                    logger.debug(f"Boosting confidence for '{name_value}' from {confidence} to {context_adjusted_confidence} due to name-asking context")

                    # Boost confidence if both models agreed
                    if models_agreed:
                        # Boost confidence by an additional 10% if both models agreed
                        context_adjusted_confidence = min(context_adjusted_confidence * 1.1, 1.0)
                        logger.debug(f"Boosting confidence for '{name_value}' to {context_adjusted_confidence} due to model agreement")

                    # Determine if confirmation is needed
                    # Always confirm ambiguous names regardless of confidence
                    needs_confirmation = is_ambiguous or context_adjusted_confidence < self.confidence_threshold

                    # Add confidence metadata to the message for form validation to use
                    name_confidence_metadata = {
                        "name_confidence": context_adjusted_confidence,
                        "name_needs_confirmation": needs_confirmation,
                        "name_candidate": name_value,
                        "is_ambiguous_name": is_ambiguous,
                        "is_name_context": is_name_context,
                        "models_agreed": models_agreed
                    }

                    # Set metadata for the form validation to use
                    message.set("name_confidence_metadata", name_confidence_metadata)

                    logger.debug(f"Found name '{name_value}' with confidence {confidence} (adjusted: {context_adjusted_confidence}), "
                               f"is_ambiguous={is_ambiguous}, is_name_context={is_name_context}, models_agreed={models_agreed}")

                    # Only add as entity if confidence is above low threshold
                    # For name-asking contexts, we lower the threshold slightly
                    effective_threshold = max(self.low_confidence_threshold * 0.8, 0.3)  # Lower threshold but not below 0.3

                    if context_adjusted_confidence >= effective_threshold:
                        extracted_entities.append({
                            "entity": "name",
                            "value": name_value,
                            "start": highest_confidence_entity['start'],
                            "end": highest_confidence_entity['end'],
                            "confidence": context_adjusted_confidence,
                            "extractor": "IndicNERFallback"
                        })
                    else:
                        logger.debug(f"Name '{name_value}' confidence {context_adjusted_confidence} below threshold {effective_threshold}, not adding as entity")

                # If no name entity was found but we're in a name-asking context, try to use the raw input
                elif text and len(text.strip()) > 1:
                    # This is a fallback for when no model detects a name but we're explicitly asking for one
                    # We'll set a low confidence and require confirmation
                    logger.debug(f"No name detected by models, but in name-asking context. Using raw input: '{text}'")

                    # Check if the raw input is an ambiguous name
                    is_ambiguous = self._is_ambiguous_name(text.strip())

                    name_confidence_metadata = {
                        "name_confidence": 0.4,  # Low confidence to ensure confirmation
                        "name_needs_confirmation": True,
                        "name_candidate": text.strip(),
                        "is_ambiguous_name": is_ambiguous,
                        "is_name_context": True,
                        "is_fallback_detection": True,
                        "models_agreed": False
                    }

                    message.set("name_confidence_metadata", name_confidence_metadata)

            except Exception as e:
                logger.error(f"Error during IndicNERFallback processing: {e}")
                # Add traceback for better debugging
                import traceback
                logger.error(traceback.format_exc())

            if extracted_entities:
                all_entities = existing_entities + extracted_entities
                message.set("entities", all_entities)

        return messages