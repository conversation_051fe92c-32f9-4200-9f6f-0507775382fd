version: "3.1"

nlu:
  - intent: greet
    examples: |
      - hey
      - hello
      - hi
      - good morning
      - good evening
      - hey there
      - sup
      - howdy

  - intent: bye
    examples: |
      - goodbye
      - cya
      - see you around
      - bye bye
      - farewell
      - later
      - chat soon

  - intent: affirm
    examples: |
      - yes
      - indeed
      - of course
      - that sounds good
      - correct
      - right
      - confirm
      - yeah
      - ok
      - sounds good

  - intent: deny
    examples: |
      - no
      - no thank you
      - nah
      - nope
      - never
      - not now
      - not needed
      - I'm good
      - I don't want that
      - I don't think so
      - absolutely not
      - definitely not
      - don't like that
      - I'd rather not
      - leave it
      - cancel that
      - not really
      - I'm not interested


  - intent: thank_you
    examples: |
      - thanks
      - thank you
      - thanks a lot
      - appreciate it
      - thank you so much
      - thanks a ton
      - thank you very much
      - thank you for your help
      - thank you for the information
      - thank you for the assistance
      - thank you for the support
      - thank you for the guidance
      - thank you for the advice
      - thank you for the information
      - thx
      - thnx
      - thankyou
      - ty
      - tysm
      - thks
      - thx a lot
      - thanks!
      - thank u
      - thank u so much
      - thank you!
      - thanks for that
      - thanks for your help
      - thanks for the info
      - thanks for booking
      - thanks for the appointment
      - thanks for scheduling
      - thanks for the confirmation
      - thanks for the booking
      - thanks for the slot
      - thanks for the time
      - thanks for the information
      - thanks for the assistance
      - thanks for the support
      - thanks for the guidance
      - thanks for the advice



  - intent: request_appointment
    examples: |
      - I want to book an appointment
      - book an appointment for me
      - Appointment book
      - book appoinment for me
      - book appoinment
      - can I schedule something?
      - I'd like to make an appointment
      - schedule a booking
      - set up a time
      - I need to make a booking
      - book a session
      - schedule a meeting
      - I'd like to schedule an appointment
      - can I book a time slot?
      - I want to schedule a session
      - can you arrange a booking for me?
      - I'd like to reserve a time
      - can you book me in for a consultation?
      - set up an appointment for me
      - can you schedule my visit?
      - I need to book an appointment
      - can I make a reservation?
      - help me book a session
      - please reserve an appointment for me
      - can I make an appointment for tomorrow?
      - I want to book a slot
      - schedule a visit for me
      - how do I book a consultation?
      - I'd like to schedule a meeting with a doctor
      - can I book a doctor's appointment?
      - reserve a spot for me
      - I would like to arrange an appointment
      - can you set up a meeting time?
      - I need to book an appointment for physiotherapy
      - when can I make an appointment?
      - can I reserve a spot for a therapy session?
      - I need to schedule a doctor's visit
      - I'd like to book an appointment at your clinic
      - can you book me in for a check-up?
      - I want to set up a consultation
      - schedule me for a therapy session

  - intent: ask_clinic_address
    examples: |
      - what is your address?
      - where are you located?
      - address please
      - can you tell me where the clinic is?
      - your location
      - address of arham physiotherapy
      - where is your clinic located?
      - what is the address of your office?
      - can you tell me your clinic's location?
      - where can I find you?
      - what's your full address?
      - where is Arham Physiotherapy situated?
      - can I get your address details?
      - where is your clinic based?
      - what's the physical address of your clinic?
      - give me the address of your clinic
      - how do I find your office?
      - can you share your clinic location with me?
      - what's the address of your center?
      - can you tell me where you are located?
      - where do I visit for treatment?
      - can you provide your location details?
      - where is your clinic in town?
      - where do I go for my appointment?
      - can I have the clinic address?
      - tell me where your clinic is
      - what's the location of Arham Physiotherapy?
      - where are you situated?
      - what's your clinic's physical address?
      - can you give me directions to your clinic?
      - tell me your exact location
      - what's your address for booking?
      - how do I get to your clinic?
      - where is your clinic near me?


  - intent: ask_clinic_hours
    examples: |
      - what are your operating hours?
      - when are you open?
      - what time do you close?
      - clinic timings
      - working hours
      - when can I visit?
      - when are you open for appointments?
      - can you tell me your clinic timings?
      - what time do you open and close?
      - what are your working hours?
      - when can I come in for an appointment?
      - what are your hours of operation?
      - what time do you start and end the day?
      - when do you close?
      - can I visit during your working hours?
      - what are the clinic's opening hours?
      - tell me the clinic hours
      - when can I schedule a visit with you?
      - how late do you stay open?
      - what time do you open for consultations?
      - what is the best time to visit?
      - what are your business hours?
      - do you have evening hours?
      - when does your clinic open?
      - when do you close for the day?
      - tell me the working hours of your clinic
      - can I visit after 5 PM?
      - what time are you available for appointments?
      - what time can I come in for treatment?
      - when is your clinic open?
      - do you have weekend hours?
      - when do you open for the first appointment?
      - are you open on Sundays?
      - what is the latest time I can book an appointment?


  - intent: ask_clinic_phone
    examples: |
      - what is your phone number?
      - contact number
      - how can I call you?
      - phone number please
      - what's the best number to reach you?
      - can I get your phone number?
      - can I get your contact number?
      - can you share your phone number?
      - how can I contact you by phone?
      - what's your contact number?
      - give me your phone number
      - what's the number to reach your clinic?
      - could you tell me the best way to call you?
      - what's your clinic's phone number?
      - how do I get in touch with you?
      - can I have your contact info?
      - how can I reach you on the phone?
      - what is the number to call your office?
      - give me a number to speak with you
      - can you provide your clinic's contact number?
      - can I get the phone number?
      - I need to call your clinic, what's the number?
      - what's the contact number to book appointments?
      - phone details please
      - what number should I dial to talk to you?
      - how do I call your clinic for help?
      - can you tell me your office number?
      - I'd like your contact number, please
      - what's the number for inquiries?
      - how do I contact your clinic directly?
      - what's the best number for appointments?
      - what's your clinic's direct line?
      - how can I get in touch with your office?
      - what phone number can I reach you at?
      - can I get a phone number for your staff?
      - tell me your office's phone number
      - what's the number for your customer service?

  - intent: ask_clinic_website
    examples: |
      - what is your website?
      - website address
      - do you have a website?
      - link to your website
      - what is your website address?
      - can you share your website link?
      - how do I visit your website?
      - give me your web address
      - send me your clinic website
      - where can I find your website?
      - what's the URL of your clinic?
      - where's your site?
      - could you provide your site address?
      - drop your website link here
      - I need your website
      - do you have an official site?
      - tell me your clinic's web address
      - where do I go online to check your services?
      - how can I access your online portal?
      - can I get the website?
      - I'd like to see your website
      - your website, please
      - is there a site I can visit?
      - give me your online details
      - link to your homepage?
      - can you give me your clinic's web page?
      - do you guys have an online page?
      - where can I browse your services online?
      - point me to your official website
      - where can I see your treatments online?
      - clinic URL please
      - show me your web link
      - how do I find you online?
      - what's your digital address?
      - any link to your official page?

  - intent: ask_clinic_services
    examples: |
      - what services do you offer?
      - list your services
      - what can I book an appointment for?
      - tell me about your treatments
      - what programs do you have?
      - what kind of physiotherapy do you do?
      - what kind of services do you offer?
      - what types of treatments are available?
      - can you walk me through your services?
      - what do you specialize in?
      - which therapies can I choose from?
      - what health services do you offer?
      - do you have any specific medical programs?
      - what kind of rehab support is offered?
      - tell me your areas of treatment
      - what all can I come in for?
      - what options are there for therapy?
      - which services can I get an appointment for?
      - what kind of support do you give to patients?
      - could you give me a list of all your services?
      - what does your clinic offer?
      - what are the available health programs?
      - what services can I consult you for?
      - can I know about your treatment categories?
      - what services can I get there?
      - what treatments do you provide?
      - what kind of medical care do you offer?
      - what health-related services are offered here?
      - do you handle physiotherapy or similar treatments?
      - what sort of services do you provide?
      - can I get info on all your treatments?
      - do you have a brochure of your services?
      - is there a list of care programs?
      - I want to explore your available programs
      - what health solutions do you provide?
      - which conditions do you treat?
      - what's included in your treatment plans?
      - how can I know what services you have?
      - do you have a menu of treatments?
      - is there a variety of care services I can choose?
      - I'd like to know the range of services available
      - do you offer more than one type of treatment?
      - what are the specialties you offer?
      - which programs do patients usually book?
      - how many services do you have?
      - what's your complete service list?
      - give me an overview of your care services
  # --- New Intent for Specific Service Details ---
  # This intent will be used when the user asks *specifically* about a service,
  # often with the service_type entity present.
  # The inform intent is used when they PROVIDE service info (like "I want weight loss").
  # This new intent is for ASKING about it.
  - intent: ask_about_service
    examples: |
      - What does [Physical Therapy & Comprehensive Pain Management](service_type) involve?
      - Could you give me information on [Weight Loss Programs](service_type)?
      - How does [Neurological Rehabilitation](service_type) work?
      - I'd like to know about [Orthopedic Rehabilitation](service_type)
      - Can you provide insights into [Gynaecology Rehabilitation & Prenatal Care](service_type)?
      - What do you offer under [physiotherapy](service_type)?
      - Can I get some info on [weight loss](service_type)?
      - I'd like details on [neuro rehab](service_type)
      - What's included in [ortho rehab](service_type)?
      - Please explain [prenatal care](service_type)
      - What is the goal of [PT](service_type)?
      - Is [Physical Therapy & Comprehensive Pain Management](service_type) suitable for chronic pain?
      - I'm curious about your [Weight Loss Programs](service_type)
      - Can you share more on [Neurological Rehabilitation](service_type)?
      - What is [Orthopedic Rehabilitation](service_type) exactly?
      - Do you offer [Gynaecology Rehabilitation & Prenatal Care](service_type)?
      - I want to understand [physiotherapy](service_type) better
      - How do you assist with [weight loss](service_type)?
      - What are the benefits of [neuro rehab](service_type)?
      - Explain the purpose of [ortho rehab](service_type)
      - Tell me how [prenatal care](service_type) is managed here
      - What does [PT](service_type) typically include?
      - How can [Physical Therapy & Comprehensive Pain Management](service_type) help me?
      - Do you have programs for [Weight Loss Programs](service_type)?
      - Clarify what [Neurological Rehabilitation](service_type) entails
      - Provide some info on [Orthopedic Rehabilitation](service_type)
      - Is there any special support for [Gynaecology Rehabilitation & Prenatal Care](service_type)?
      - What's involved in a [physiotherapy](service_type) session?
      - Can I get a breakdown of your [weight loss](service_type) offerings?
      - What is meant by [neuro rehab](service_type)?
      - I'd like to learn more about [ortho rehab](service_type)
      - Give me a summary of your [prenatal care](service_type) services
      - Can you explain the [PT](service_type) approach here?
      - What does a typical [Physical Therapy & Comprehensive Pain Management](service_type) plan look like?
      - How do [Weight Loss Programs](service_type) function at your facility?
      - What kind of patients need [Neurological Rehabilitation](service_type)?
      - When is [Orthopedic Rehabilitation](service_type) recommended?
      - Do you offer care in [Gynaecology Rehabilitation & Prenatal Care](service_type)?
      - Who is [physiotherapy](service_type) for?
      - Can I book a consultation for [weight loss](service_type)?
      - What do people gain from [neuro rehab](service_type)?
      - Is [ortho rehab](service_type) done with machines or manually?
      - When should I start [prenatal care](service_type)?
      - Can you define [PT](service_type) in simple terms?
      - What are your treatment methods in [Physical Therapy & Comprehensive Pain Management](service_type)?
      - Any diet included in [Weight Loss Programs](service_type)?
      - Do neurologists oversee [Neurological Rehabilitation](service_type)?
      - What to expect in [Orthopedic Rehabilitation](service_type)?
      - How is [Gynaecology Rehabilitation & Prenatal Care](service_type) structured?
      - I heard about [physiotherapy](service_type), what does it include?
      - Can you treat obesity through [weight loss](service_type) services?
      - Is [neuro rehab](service_type) covered by insurance?
      - What activities are done during [ortho rehab](service_type)?
      - Can you start [prenatal care](service_type) in the first trimester?
      - What exactly is [PT](service_type)?
      - Is [Physical Therapy & Comprehensive Pain Management](service_type) painful?
      - How much weight can I lose in your [Weight Loss Programs](service_type)?
      - What kind of exercises are part of [Neurological Rehabilitation](service_type)?
      - How long does [Orthopedic Rehabilitation](service_type) usually last?
      - What conditions does [Gynaecology Rehabilitation & Prenatal Care](service_type) help with?
      - Can you explain [physiotherapy](service_type) in layman terms?
      - Are there experts for [weight loss](service_type) counseling?
      - Can [neuro rehab](service_type) help with stroke recovery?
      - What all is done in [ortho rehab](service_type)?
      - Is [prenatal care](service_type) only for high-risk pregnancies?
      - What does [PT](service_type) look like on a weekly basis?
      - What's the typical duration of [Physical Therapy & Comprehensive Pain Management](service_type)?
      - Is exercise part of [Weight Loss Programs](service_type)?
      - Can [Neurological Rehabilitation](service_type) help with motor skills?
      - What are stages of [Orthopedic Rehabilitation](service_type)?
      - Do you include family education in [Gynaecology Rehabilitation & Prenatal Care](service_type)?
      - What happens during [physiotherapy](service_type)?
      - What makes your [weight loss](service_type) program unique?
      - I want to know about [neuro rehab](service_type) therapy steps
      - What kind of monitoring is done in [ortho rehab](service_type)?
      - Do I need referrals for [prenatal care](service_type)?
      - What's your approach to [PT](service_type)?
      - Is [Physical Therapy & Comprehensive Pain Management](service_type) available for arthritis?
      - Do you customize [Weight Loss Programs](service_type) for each person?
      - Can [Neurological Rehabilitation](service_type) help with brain injuries?
      - Does [Orthopedic Rehabilitation](service_type) involve pain relief?
      - What to expect from [Gynaecology Rehabilitation & Prenatal Care](service_type) sessions?
      - What is included in a standard [physiotherapy](service_type) plan?
      - Is [weight loss](service_type) program doctor supervised?
      - Who benefits most from [neuro rehab](service_type)?
      - Is there a standard protocol for [ortho rehab](service_type)?
      - What do I need to begin [prenatal care](service_type)?
      - Can seniors take part in [PT](service_type)?
      - How often are [Physical Therapy & Comprehensive Pain Management](service_type) sessions scheduled?
      - Will your [Weight Loss Programs](service_type) include medical tests?
      - Can kids undergo [Neurological Rehabilitation](service_type)?
      - What's the typical setting for [Orthopedic Rehabilitation](service_type)?
      - Is yoga part of [Gynaecology Rehabilitation & Prenatal Care](service_type)?
      - Are the [physiotherapy](service_type) sessions long?
      - Is there counseling in your [weight loss](service_type) program?
      - Do you assist with recovery in [neuro rehab](service_type)?
      - Is [ortho rehab](service_type) good for post-fracture healing?
      - Is [prenatal care](service_type) done weekly or monthly?
      - Are [PT](service_type) sessions customized per condition?
      - What kind of therapies do you offer?
      - Can you tell me what services you provide?
      - I want to know about your health programs
      - What rehab services are available?
      - Do you offer any pain management services?
      - Tell me about the care options you have
      - What do you offer for weight loss?
      - I'm interested in your physiotherapy sessions
      - Can you help with neurological recovery?
      - What type of physical treatments do you provide?
      - Is prenatal care available?
      - Any support for orthopedic recovery?
      - What does your therapy program cover?
      - What kind of patient care do you offer?
      - Can I get help with obesity?
      - Do you have programs for pregnancy support?
      - What services are there for bone injuries?
      - Do you have fitness or diet-related programs?
      - How do you help people with chronic pain?
      - What are your physiotherapy options?
      - Can someone guide me through your therapy plans?
      - Do you help stroke patients recover?
      - What programs help with mobility issues?
      - Is there any physical rehab for sports injuries?
      - Do you offer female health support?
      - I'm looking for treatment for my back pain
      - Can you guide me about post-surgery rehabilitation?
      - Do you have health improvement packages?
      - What treatments are available for joint pain?
      - Can someone explain your therapy offerings?

  # --- Intent for appointment verification ---
  - intent: verify_appointment
    examples: |
      - I want to check my appointment
      - verify my appointment
      - can you check my appointment details
      - I need to verify my appointment
      - appointment verification
      - check my booking
      - can you tell me about my appointment
      - I want to know my appointment details
      - verify my booking
      - my appointment ID is [45](appointment_id)
      - appointment [123](appointment_id)
      - booking number [789](appointment_id)
      - ID [456](appointment_id)
      - reference [234](appointment_id)
      - appointment ID [567](appointment_id)
      - my booking ID is [890](appointment_id)
      - verify appointment [345](appointment_id)
      - check booking [678](appointment_id)
      - appointment number [901](appointment_id)
      - check appointment status
      - I want to confirm my appointment
      - appointment details please
      - can you verify my appointment
      - I need to check my booking
      - show me my appointment
      - what are the details of my appointment
      - I'd like to verify my booking
      - can you check if I have an appointment
      - I need information about my appointment
      - verify my scheduled appointment
      - check my appointment information
      - I want to see my appointment details
      - can you tell me when my appointment is
      - I need to know about my booking
      - appointment verification please
      - check my appointment record
      - I want to confirm my booking details
      - verify my scheduled time
      - can you check my appointment status
      - I need to see my appointment information

  # --- New Intent for FAQs ---
  # You can define a single 'faq' intent or more specific ones like 'faq_walk_ins', 'faq_policies', etc.
  # A single 'faq' intent is simpler to start, and you'd train it with many questions.
  - intent: faq
    examples: |
      - do you offer walk-ins?
      - what is your cancellation policy?
      - do I need a referral?
      - what should I bring to my first appointment?
      - is parking available?
      - what insurances do you accept?
      - how long is an appointment?
      - can I reschedule an appointment?
      - what are your covid policies?
      - how do I prepare for a session?
      - what are your payment options?

  - intent: inform
    examples: |
      - my name is [neha bhardwaj](name)
      - I am [John Doe](name)
      - call me [Priya Sharma](name)
      - it's [Ahmed Khan](name)
      - you can note down my name as [Maria Garcia](name)
      # Ambiguous names that could be common nouns
      - my name is [Honey](name)
      - I am [Rose](name)
      - call me [Joy](name)
      - it's [Faith](name)
      - my name is [Hope](name)
      - I am [Grace](name)
      - call me [Sky](name)
      - it's [Sunny](name)
      - my name is [Amber](name)
      - I am [Crystal](name)
      - call me [Ruby](name)
      - it's [Pearl](name)
      - my name is [Jade](name)
      - I am [Jasmine](name)
      - call me [Lily](name)
      - it's [Daisy](name)
      - my name is [Star](name)
      - I am [Moon](name)
      - call me [Dawn](name)
      - it's [Dusk](name)
      - my name is [Rain](name)
      - I am [Cloud](name)
      - call me [River](name)
      - it's [Ocean](name)
      # Ambiguous names with last names
      - my name is [Honey Singh](name)
      - I am [Rose Patel](name)
      - call me [Joy Kumar](name)
      - it's [Faith Johnson](name)
      - my name is [Hope Williams](name)
      - I am [Grace Smith](name)
      - call me [Sky Brown](name)
      - it's [Sunny Deol](name)
      - my name is [Amber Heard](name)
      - I am [Crystal Lee](name)
      - call me [Ruby Rose](name)
      - it's [Pearl White](name)
      - my name is [Jade Green](name)
      - I need [Physical Therapy](service_type)
      - book a session for [Comprehensive Pain Management](service_type)
      - interested in [Physical Therapy & Comprehensive Pain Management](service_type)
      - [Physical Therapy](service_type) appointment
      - [Pain Management](service_type) session
      - need [pain management](service_type)
      - [Physio Therapy](service_type)
      - [Pain Managment](service_type)
      - [Physical Therapy](service_type) and [Pain Management](service_type)
      - [PT](service_type)
      - booking for [Physical Therpay](service_type)
      - schedule [pain mngmnt](service_type)
      - interested in [Weight Loss Programs](service_type)
      - tell me about [Weight Loss Programs](service_type)
      - book a [Weight Loss Program](service_type) session
      - schedule for [weight loss](service_type)
      - [Weight Loss](service_type) appointment
      - [Weightlos](service_type)
      - [weight lose](service_type)
      - [weightloss](service_type)
      - [Weight Loss Programms](service_type)
      - [Weight Lossing](service_type)
      - i want [weight loss](service_type)
      - [weightlos ](service_type)
      - [weightlose](service_type)
      - [weightloss](service_type)
      - can i get [weightloss service](service_type)
      - [weight lose program](service_type)
      - I need [Neurological Rehabilitation](service_type)
      - looking for [Neurological Rehabilitation](service_type)
      - book a [Neurological Rehabilitation](service_type) appointment
      - schedule [Neurological Rehabilitation](service_type)
      - [Neurological Rehab](service_type)
      - [Neuro Rehabilitation](service_type)
      - [Neuro Rehab](service_type)
      - [Neurological Reabilitation](service_type)
      - [Neuro Rehab Appointment](service_type)
      - [Neurological Rehab](service_type) session
      - I need [Orthopedic Rehabilitation](service_type)
      - looking for [Orthopedic Rehabilitation](service_type)
      - book a [Orthopedic Rehabilitation](service_type) appointment
      - schedule [Orthopedic Rehabilitation](service_type)
      - [Orthopedic Rehab](service_type)
      - [Ortho Rehabilitation](service_type)
      - [Ortho Rehab](service_type)
      - [Orthopaedic Rehabilitation](service_type)
      - [Ortho Rehab Appointment](service_type)
      - [Orthopedic Rehab](service_type) session
      - interested in [Gynaecology Rehabilitation & Prenatal Care](service_type)
      - need [Gynaecology Rehabilitation](service_type)
      - book [Gynaecology & Prenatal Care](service_type)
      - schedule [Gynaecology Rehab](service_type)
      - [Prenatal Care](service_type) appointment
      - about [Gynaecology Rehabilitation & Prenatal Care](service_type)
      - [Gyno Rehab](service_type)
      - [Prenatal](service_type)
      - [Gynecology Rehabilitation](service_type)
      - [Gynaecology and Prenatal](service_type)
      - [Gynaecology Rehab](service_type) appointment
      - book [prenatal care](service_type)

      - on [monday](date)
      - book for [tomorrow](date)
      - schedule on [next friday](date)
      - I want an appointment on [november 15th](date)
      - let's do [today](date)
      - how about [next week](date)
      - book it for [this tuesday](date)
      - I'd like it on [May 10th](date)
      - can we do [next monday](date)
      - schedule for [this weekend](date)
      - [tomorrow](date) works
      - [Monday](date) would be good
      - I prefer [next Tuesday](date)
      - let's schedule for [May 20](date)
      - [May 20th](date) please
      - [20 May](date) works for me
      - [the 20th](date) of this month
      - on the [fifth](date) of next month
      - [1st of next month](date)
      - [Dec 25th](date) if possible
      - I want it on [Monday](date)

      - at [3pm](time)
      - around [10am](time)
      - [in the afternoon](time)
      - schedule for [noon](time)
      - [9 AM](time) works for me
      - how about [4 o'clock](time)
      - [morning](time) appointment
      - in the [evening](time)
      - can we do [2:30pm](time)
      - [5:45](time) would work
      - [11 o'clock](time) appointment
      - [6pm](time) please
      - [after lunch](time)
      - [before noon](time)
      - around [7 in the evening](time)
      - [8:15 am](time) if available

      - [tomorrow](date) at [3pm](time)
      - [next friday](date) at [10am](time)
      - [Monday](date) [morning](time)
      - [this Tuesday](date) at [9 AM](time)
      - [May 10th](date) in the [afternoon](time)
      - [next monday](date) [morning](time)
      - [May 20th](date) at [2pm](time)
      - [20 May](date) [3pm](time)
      - [next week](date) in the [evening](time)

      - my name is [Alice](name)
      - I'm [Bob](name)
      - [neel](name)
      - [bob](name)
      - [sakshi](name)
      - [bhargav](name)
      - [sagar](name)
      - [sonu](name)
      - [ankit](name)
      - [henny](name)
      - [roman](name)
      - [jass](name)
      - [rishi](name)
      - [abhishek](name)
      - [ananya](name)
      - [akshay](name)
      - [shivam](name)
      - [shivani](name)
      - [shreya](name)
      - [sonaakshi](name)
      - [prachi](name)
      - [shivang](name)
      - [meet](name)
      - [ayush](name)
      - [tanvi](name)
      - [harshit](name)
      - [sneha](name)
      - [manav](name)
      - [riya](name)
      - [viraj](name)
      - [isha](name)
      - [aarav](name)
      - [kiran](name)
      - [abhishek verma](name)
      - [ananya sharma](name)
      - [akshay mehta](name)
      - [shivam agrawal](name)
      - [shivani joshi](name)
      - [shreya patel](name)
      - [sonaakshi rathore](name)
      - [prachi jain](name)
      - [shivang sehgal](name)
      - [meet bhatia](name)
      - [ayush malik](name)
      - [tanvi kapoor](name)
      - [harshit saxena](name)
      - [sneha gupta](name)
      - [manav singh](name)
      - [riya kaur](name)
      - [viraj solanki](name)
      - [isha rawat](name)
      - [aarav yadav](name)
      - [kiran choudhary](name)
      - [aditya rana](name)
      - [neha bhardwaj](name)
      - [yash thakur](name)
      - [tanya rajput](name)
      - [rohit bansal](name)
      - [diya gill](name)
      - [arjun nanda](name)
      - [kavya lamba](name)
      - [rajat vora](name)
      - [simran sood](name)
      - [devansh bhalla](name)
      - [mehak kohli](name)
      - [nikhil oberoi](name)
      - [payal sehrawat](name)
      - [vivek ahuja](name)
      - [tamanna nagpal](name)
      - [kunal bedi](name)
      - [krishna grover](name)
      - [palak suri](name)
      - [raghav mahajan](name)
      - my name is [Charlie](name)
      - I am [John](name)
      - my name is [Charlie](name)
      - I am [Nil](name)
      - my name is [Sarah Johnson](name)
      - I am [Peter Jones](name)
      - you can note down [David Miller](name)
      - it's [Emily](name)
      - I'm called [Michael](name)
      - my first name is [Robert](name)
      - the name is [Jessica](name)
      - book it under [Chris Evans](name)
      - my name's [Daniel Wilson](name)
      - I'm [Laura Davis](name)
      - my name is [James Brown](name)
      - it's [Sophia Garcia](name)
      - I am [William Rodriguez](name)
      - call me [Olivia Martinez](name)
      - my name is [Ethan Lee](name)
      - [Ava Chen](name)
      - I'm [Mia Kim](name)
      - my name is [Noah Singh](name)
      - it's [Liam Patel](name)
      - [Isabella Wong](name)
      - I am [Lucas Taylor](name)
      - my name is [Arthur Dent](name)
      - my name is [Reya](name)
      - [Tanmay Kulkarni](name)
      - I'm [Ishita](name)
      - just [Kabir Sinha](name)
      - the name is [Aria](name)
      - call me [Rudra Kapoor](name)
      - [Meera](name)
      - [Rishi Verma](name)
      - book under [Anaya](name)
      - it's [Advik](name)
      - my first name is [Kaira](name)
      - [Aryan Khurana](name)
      - [Diya](name)
      - please register [Vihaan Bansal](name)
      - [Shaurya](name)
      - I am [Ira Mukherjee](name)
      - [Sahil](name)
      - [Aarohi Sharma](name)
      - I'm [Pari](name)
      - [Nikhil Malhotra](name)
      - I go by [Sneha](name)
      - [Pranav Rathi](name)
      - just [Reyansh](name)
      - name is [Lavanya Kapoor](name)
      - [Aniket](name)
      - book it for [Trisha Mehta](name)
      - [Raghav](name)
      - it's [Simran Ahuja](name)
      - the name's [Kunal](name)
      - [Aanya Singh](name)
      - write down [Dev](name)
      - I'm called [Vanya Nair](name)
      - [Mihir](name)
      - note my name [Tisha Sehgal](name)
      - [Aman](name)
      - please use [Nitya Paul](name)
      - [Harsh](name)
      - go with [Tara Reddy](name)
      - I'm [Samar](name)
      - [Jiya Thomas](name)
      - my name's [Om](name)
      - [Ritika Arora](name)
      - book it under [Rudraksh Das](name)
      - [Tanvi](name)
      - it's just [Krish Malhotra](name)
      - [Manya](name)
      - I'm [Ronav Kothari](name)
      - [Avika](name)
      - I am [Yuvan Lobo](name)
      - [Ishaan Dasgupta](name)
      - [9123456780](phone_number)
      - [9012345678](phone_number)
      - [9988776655](phone_number)
      - [8899776655](phone_number)
      - [9765432109](phone_number)
      - [9654321098](phone_number)
      - [9543210987](phone_number)
      - [9432109876](phone_number)
      - [9321098765](phone_number)
      - [9210987654](phone_number)
      - [9109876543](phone_number)
      - [9098765432](phone_number)
      - [8987654321](phone_number)
      - [8876543210](phone_number)
      - [8765432109](phone_number)
      - [8654321098](phone_number)
      - [8543210987](phone_number)
      - my number is [************](phone_number)
      - phone number [0987654321](phone_number)
      - you can reach me at [(*************](phone_number)
      - it's [************](phone_number)
      - my contact is [************](phone_number)
      - my number is [555-1212](phone_number)
      - the number is [98765](phone_number)
      - here is my number: [1122334455](phone_number)
      - my cell phone is [0123456789](phone_number)
      - my contact number is [******-654-3210](phone_number)
      - you can call this number: [555-5555](phone_number)
      - it is [123456789](phone_number)
      - my mobile is [07700 900800](phone_number)
      - my phone is [************](phone_number)
      - [1234567890](phone_number)
      - my phone number is [800-RASA-BOT](phone_number)
      - my contact number is [+44 20 7946 0689](phone_number)
      - [1](appointment_id)
      - 1
      - [2](appointment_id)
      - 2
      - [3](appointment_id)
      - 3
      - [4](appointment_id)
      - 4
      - [5](appointment_id)
      - 5
      - [6](appointment_id)
      - 6
      - [7](appointment_id)
      - 7
      - [8](appointment_id)
      - 8
      - [9](appointment_id)
      - 9
      - [10](appointment_id)
      - 10
      - it's [2](appointment_id)
      - my ID is [3](appointment_id)
      - the number is [4](appointment_id)
      - booking ID [5](appointment_id)
      - code [6](appointment_id)
      - reference [7](appointment_id)
      - it's just [8](appointment_id)
      - ID [9](appointment_id)
      - I have [1](appointment_id)
      - given ID [2](appointment_id)
      - booked with ID [3](appointment_id)
      - my confirmation is [4](appointment_id)
      - appointment ID is [5](appointment_id)
      - ref [6](appointment_id)
      - it's number [7](appointment_id)
      - the ID [8](appointment_id)
      - confirm code [9](appointment_id)
      - ID number [1](appointment_id)
      - that's [2](appointment_id)

      - [11](appointment_id)
      - 11
      - [12](appointment_id)
      - 12
      - [13](appointment_id)
      - 13
      - [14](appointment_id)
      - 14
      - [15](appointment_id)
      - 15
      - [16](appointment_id)
      - 16
      - [17](appointment_id)
      - 17
      - [18](appointment_id)
      - 18
      - [19](appointment_id)
      - 19
      - [20](appointment_id)
      - [10](appointment_id)
      - it's [23](appointment_id)
      - my ID is [45](appointment_id)
      - the number is [67](appointment_id)
      - booking ID [89](appointment_id)
      - code [12](appointment_id)
      - reference [34](appointment_id)
      - it's just [56](appointment_id)
      - ID [78](appointment_id)
      - I have [90](appointment_id)
      - given ID [11](appointment_id)
      - booked with ID [22](appointment_id)
      - my confirmation is [33](appointment_id)
      - appointment ID is [44](appointment_id)
      - ref [55](appointment_id)
      - it's number [66](appointment_id)
      - the ID [77](appointment_id)
      - confirm code [88](appointment_id)
      - ID number [99](appointment_id)
      - that's [20](appointment_id)


      - [101](appointment_id)
      - 101
      - [102](appointment_id)
      - 102
      - [103](appointment_id)
      - 103
      - [104](appointment_id) 
      - 104
      - [105](appointment_id)
      - 105
      - [106](appointment_id)
      - 106
      - [107](appointment_id)
      - 107
      - [108](appointment_id)
      - 108
      - [109](appointment_id)
      - 109
      - [110](appointment_id)
      - 110
      - [100](appointment_id)
      - it's [234](appointment_id)
      - my ID is [567](appointment_id)
      - the number is [890](appointment_id)
      - booking ID [111](appointment_id)
      - code [222](appointment_id)
      - reference [333](appointment_id)
      - it's just [444](appointment_id)
      - ID [555](appointment_id)
      - I have [666](appointment_id)
      - given ID [789](appointment_id)
      - booked with ID [901](appointment_id)
      - my confirmation is [123](appointment_id)
      - appointment ID is [456](appointment_id)
      - ref [789](appointment_id)
      - it's number [102](appointment_id)
      - the ID [345](appointment_id)
      - confirm code [678](appointment_id)
      - ID number [990](appointment_id)
      - that's [210](appointment_id)

      - [1111](appointment_id)
      - 1111
      - [1112](appointment_id)
      - 1112
      - [1113](appointment_id)
      - 1113
      - [1114](appointment_id)
      - 1114
      - [1115](appointment_id)
      - 1115
      - [1116](appointment_id)
      - 1116
      - [1117](appointment_id)
      - 1117
      - [1118](appointment_id)
      - 1118  
      - [1119](appointment_id)
      - 1119
      - [1120](appointment_id)
      - 1120
      - [1000](appointment_id)
      - it's [2345](appointment_id)
      - my ID is [6789](appointment_id)
      - the number is [1212](appointment_id)
      - booking ID [3434](appointment_id)
      - code [5656](appointment_id)
      - reference [7878](appointment_id)
      - it's just [9090](appointment_id)
      - ID [1111](appointment_id)
      - I have [2222](appointment_id)
      - given ID [3333](appointment_id)
      - booked with ID [4444](appointment_id)
      - my confirmation is [5555](appointment_id)
      - appointment ID is [6666](appointment_id)
      - ref [7777](appointment_id)
      - it's number [8888](appointment_id)
      - the ID [9999](appointment_id)
      - confirm code [1230](appointment_id)
      - ID number [4560](appointment_id)
      - that's [7890](appointment_id)

  - intent: cancel_appointment
    examples: |
      - cancel my appointment
      - I need to cancel
      - please cancel my booking
      - cancel my scheduled time
      - I want to reschedule
      - can you cancel my appointment?
      - never mind the booking

  - intent: bot_challenge
    examples: |
      - are you a bot?
      - are you human?
      - am i talking to a bot?
      - who are you?
      - what are you?

  - intent: ask_available_times
    examples: |
      - check available times
      - what times are free?
      - when can I book?
      - show me open slots
      - are there any appointments available on [next tuesday](date)?
      - what are your available appointment times for [tomorrow](date)
      - can I see the schedule for [August 15th](date)
      - check availability for [today](date)
      - any openings on [next Monday](date)
      - what about [July 4th](date)
      - are you free on [the 10th](date)
      - what slots do you have for [Friday](date)
      - tell me the available times
      - what time slots are available on [15 may](date)
      - what time slots are available on [may 15](date)
      - what time slots are available for [tomorrow](date)
      - what time slots are available for [next monday](date)
      - what time slots do you have on [15th may](date)
      - what time slots do you have for [may 15th](date)
      - available slots on [15 may](date)
      - available slots for [may 15](date)
      - available times on [15 may](date)
      - available times for [may 15](date)
      - show me available slots on [15 may](date)
      - show me available slots for [may 15](date)
      - what are the available times on [15 may](date)
      - what are the available times for [may 15](date)
      - [15 may](date)
      - [may 15](date)
      - [15th may](date)
      - [may 15th](date)
      - [tomorrow](date)
      - [next monday](date)
      - [friday](date)
      - [next week](date)
      - [day after tomorrow](date)
      - [next month](date)
      - [15/05/2025](date)
      - [05/15/2025](date)
      - [2025-05-15](date)
      - [15-05-2025](date)
      - [05-15-2025](date)

entity_synonyms:
  # Map variations/typos to the official service names
  "Physical Therapy & Comprehensive Pain Management":
    - "Physical Therapy"
    - "Comprehensive Pain Management"
    - "Pain Management"
    - "Physio Therapy"
    - "Pain Managment"
    - "PT"
    - "Physical Therpay"
    - "pain mngmnt"

  "Weight Loss Programs":
    - "Weight Loss"
    - "Weightlos"
    - "weight lose"
    - "weightloss"
    - "Weight Loss Programms"
    - "Weight Lossing"
    - "weight lose program"

  "Neurological Rehabilitation":
    - "Neurological Rehab"
    - "Neuro Rehabilitation"
    - "Neuro Rehab"
    - "Neurological Reabilitation"

  "Orthopedic Rehabilitation":
    - "Orthopedic Rehab"
    - "Ortho Rehabilitation"
    - "Ortho Rehab"
    - "Orthopaedic Rehabilitation"

  "Gynaecology Rehabilitation & Prenatal Care":
    - "Gynaecology Rehabilitation"
    - "Prenatal Care"
    - "Gynaecology & Prenatal Care"
    - "Gynaecology Rehab"
    - "Prenatal"
    - "Gyno Rehab"
    - "Gynecology Rehabilitation"
    - "Gynaecology and Prenatal"
