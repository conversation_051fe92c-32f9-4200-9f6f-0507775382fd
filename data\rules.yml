version: "3.1"

rules:
  # The rule below is for the default `greet` intent.
  - rule: Say hi anytime user says hi
    steps:
      - intent: greet
      - action: utter_greet

  # The rule below is for the default `bye` intent.
  - rule: Say goodbye anytime user says goodbye
    # This rule is often made interruptible, but for now, keep it simple.
    steps:
      - intent: bye
      - action: utter_goodbye

  # The rule below is for the default `bot_challenge` intent.
  - rule: Say 'I am a bot' anytime user challenges
    steps:
      - intent: bot_challenge
      - action: utter_iamabot

  # Rule to ensure the form's validation action is called when the form is active.
  # This rule is necessary for the validate_appointment_form custom action to run.
  - rule: Validation rule for appointment form
    condition:
      # The form is active
      - active_loop: appointment_form
    steps:
      # The form's validation action is executed
      - action: validate_appointment_form
      # The form then continues to run its logic to check required slots
      - active_loop: appointment_form # Keep the loop active after validation

  # Rule to activate the form immediately if the user expresses intent to book.
  # This rule applies only when no form is active.
  - rule: Activate appointment form
    condition:
      - active_loop: null
    steps:
      - intent: request_appointment
      - action: appointment_form
      - active_loop: appointment_form  # Explicitly set the active loop

  - rule: Handle interruptions during form filling for service info
    condition:
      - active_loop: appointment_form
    steps:
      - intent: ask_about_service
      - action: action_provide_service_details
      - action: appointment_form
      - active_loop: appointment_form

  - rule: Handle consecutive misunderstandings
    condition:
      - slot_was_set:
          - requested_slot: service_type
    steps:
      - intent: nlu_fallback
      - action: action_reset_form_state
      - action: utter_default
      - action: utter_help_options

  # --- Rules for Static Information Intents (when not in form) ---
  # These rules apply when no form is currently active

  - rule: Respond to ask clinic address when not in form
    condition:
      - active_loop: null # Only run when no form is active
    steps:
      - intent: ask_clinic_address
      - action: utter_ask_clinic_address

  - rule: Respond to ask clinic hours when not in form
    condition:
      - active_loop: null # Only run when no form is active
    steps:
      - intent: ask_clinic_hours
      - action: utter_ask_clinic_hours

  - rule: Respond to ask clinic phone when not in form
    condition:
      - active_loop: null # Only run when no form is active
    steps:
      - intent: ask_clinic_phone
      - action: utter_ask_clinic_phone

  - rule: Respond to ask clinic website when not in form
    condition:
      - active_loop: null # Only run when no form is active
    steps:
      - intent: ask_clinic_website
      - action: utter_ask_clinic_website

  # Rule: Handle ask_clinic_services when not in form
  - rule: Respond to ask clinic services when not in form
    condition:
      - active_loop: null # Only run when no form is active
    steps:
      - intent: ask_clinic_services
      - action: utter_ask_clinic_services

  # Rule: Handle generic faq when not in form
  - rule: Respond to faq when not in form
    condition:
      - active_loop: null # Only run when no form is active
    steps:
      - intent: faq
      - action: utter_faq # This is the placeholder response you added in domain.yml

  # NEW RULE: Handle ask_about_service intent when not in form
  - rule: Respond to questions about specific services when not in form
    condition:
      - active_loop: null # Only run when no form is active
    steps:
      - intent: ask_about_service
      - action: action_provide_service_details # Trigger the custom action

  # --- Rules for Interruptions (Static Information Intents interrupting the form) ---
  # These rules ensure the bot responds to information queries even when the form is active
  # and then resumes the form.

  - rule: Allow ask clinic address to interrupt form
    condition:
      - active_loop: appointment_form # Apply this rule when the appointment form is active
    steps:
      - intent: ask_clinic_address # If the user has this intent
      - action: utter_ask_clinic_address # Respond with the address
      - action: appointment_form # Resume the form (Rasa remembers which slot was requested)
      - active_loop: appointment_form # <--- Explicitly set the active loop after resuming

  - rule: Allow ask clinic hours to interrupt form
    condition:
      - active_loop: appointment_form
    steps:
      - intent: ask_clinic_hours
      - action: utter_ask_clinic_hours
      - action: appointment_form
      - active_loop: appointment_form # <--- Explicitly set the active loop after resuming

  - rule: Allow ask clinic phone to interrupt form
    condition:
      - active_loop: appointment_form
    steps:
      - intent: ask_clinic_phone
      - action: utter_ask_clinic_phone
      - action: appointment_form
      - active_loop: appointment_form # <--- Explicitly set the active loop after resuming

  - rule: Allow ask clinic website to interrupt form
    condition:
      - active_loop: appointment_form
    steps:
      - intent: ask_clinic_website
      - action: utter_ask_clinic_website
      - action: appointment_form
      - active_loop: appointment_form # <--- Explicitly set the active loop after resuming

  # Rule to allow ask_clinic_services to interrupt form
  # After providing the list of services, the form will resume and ask for the service type again.
  - rule: Allow ask clinic services to interrupt form
    condition:
      - active_loop: appointment_form
    steps:
      - intent: ask_clinic_services
      - action: utter_ask_clinic_services # Respond with the list of services
      - action: appointment_form # Resume the form
      - active_loop: appointment_form # <--- Explicitly set the active loop after resuming

  # NEW RULE: Allow ask_about_service to interrupt form
  - rule: Allow ask about specific service to interrupt form
    condition:
      - active_loop: appointment_form
    steps:
      - intent: ask_about_service
      - action: action_provide_service_details # Provide service details
      - action: appointment_form # Resume the form
      - active_loop: appointment_form # <--- Explicitly set the active loop after resuming

  # Rule to allow generic faq to interrupt form
  - rule: Allow generic faq to interrupt form
    condition:
      - active_loop: appointment_form
    steps:
      - intent: faq
      - action: utter_faq # Respond with the generic faq placeholder (utter_faq)
      - action: appointment_form # Resume the form
      - active_loop: appointment_form # <--- Explicitly set the active loop after resuming

  # If user says "yes" after we provided details, resume the form
  - rule: Resume booking after service‐details yes
    condition:
      - active_loop: null # no form active now
    steps:
      - action: action_provide_service_details # we just gave details
      - intent: affirm # user says "yes"
      - action: appointment_form # restart the form
      - active_loop: appointment_form # now collect next slot

  - rule: Handle user saying no
    steps:
      - intent: deny
      - action: utter_ack_deny

  - rule: Acknowledge a "no"
    steps:
      - intent: deny
      - action: utter_ack_deny

  # ADD/ENSURE THIS RULE EXISTS for direct booking after form completion:
  - rule: Submit appointment form and book directly
    condition:
      - active_loop: appointment_form # Form is active
    steps:
      - action: appointment_form     # Form runs, validates the last slot, fills it.
                                      # If all slots are filled, form deactivates.
      - active_loop: null           # This signifies the form has successfully collected all slots and deactivated.
      - action: action_book_appointment # Directly book the appointment
      - action: action_listen         # Listen for next user input

  - rule: Handle asking for available times
    steps:
      - intent: ask_available_times
      - action: action_check_availability
      - action: action_listen # Ensure the bot listens after the custom action

  # Single rule to handle thank you intent in any context
  - rule: Respond to thank you
    steps:
      - intent: thank_you
      - action: utter_thank_you

  # --- Rules for Appointment Verification ---

  # Rule to activate the appointment verification form
  - rule: Activate appointment verification form
    condition:
      - active_loop: null
    steps:
      - intent: verify_appointment
      - action: appointment_verification_form
      - active_loop: appointment_verification_form

  # Rule for slot extraction and form validation
  - rule: Extract slots and validate appointment verification form
    condition:
      - active_loop: appointment_verification_form
    steps:
      - action: action_extract_appointment_verification_slots  # Extract slots first
      - action: validate_appointment_verification_form  # Then validate them
      - active_loop: appointment_verification_form # Keep form active while validating

  # Rule to handle user input during appointment verification form
  - rule: Handle user input during appointment verification form
    condition:
      - active_loop: appointment_verification_form
    steps:
      - intent: inform
      - action: action_extract_appointment_verification_slots  # Extract slots from inform intent
      - action: validate_appointment_verification_form  # Validate the extracted slots
      - active_loop: appointment_verification_form  # Keep the form active

  # Rule to handle form submission when both slots are filled
  - rule: Submit appointment verification form when complete
    condition:
      - active_loop: appointment_verification_form
      - slot_was_set:
        - appointment_id
        - phone_number
    steps:
      - action: action_extract_appointment_verification_slots
      - action: validate_appointment_verification_form
      - action: action_get_appointment_details
      - active_loop: null
      - action: action_listen

  # Rule to handle thank_you intent in a neutral context (no active form)
  - rule: Handle thank_you in neutral context
    condition:
      - active_loop: null
    steps:
      - intent: thank_you
      - action: utter_thank_you
      - action: action_listen

  # Remove all other conflicting rules for appointment verification form completion
  # The rules below are commented out because they're redundant or conflicting

  # Rule to handle form submission when both slots are filled
  # - rule: Submit appointment verification form when complete
  #   condition:
  #     - active_loop: appointment_verification_form
  #     - slot_was_set:
  #       - appointment_id
  #       - phone_number
  #   steps:
  #     - action: action_extract_appointment_verification_slots
  #     - action: validate_appointment_verification_form

  # Rule to handle form submission when phone number is provided
  # - rule: Submit appointment verification form with phone number
  #   condition:
  #     - active_loop: appointment_verification_form
  #     - slot_was_set:
  #       - phone_number
  #   steps:
  #     - action: action_extract_appointment_verification_slots
  #     - action: validate_appointment_verification_form

  # Rule to properly deactivate the appointment verification form after completion
  # - rule: Deactivate appointment verification form after completion
  #   condition:
  #     - active_loop: appointment_verification_form
  #     - slot_was_set:
  #         - phone_number: true
  #         - appointment_id: true
  #   steps:
  #     - action: validate_appointment_verification_form
  #     - active_loop: null
  #     - slot_was_set:
  #         - requested_slot: null

  # Rule for handling cancellation intent during the appointment verification form
  - rule: Handle cancel during appointment_verification_form
    condition:
      - active_loop: appointment_verification_form
    steps:
      - intent: deny
      - action: utter_ack_deny
      - action: action_deactivate_loop
      - active_loop: null

  # --- Rules for Appointment Cancellation ---

  # Rule to activate the appointment cancellation form
  - rule: Activate appointment cancellation form
    condition:
      - active_loop: null
    steps:
      - intent: cancel_appointment
      - action: appointment_cancellation_form
      - active_loop: appointment_cancellation_form

  # Rule for slot extraction and form validation
  - rule: Extract slots and validate appointment cancellation form
    condition:
      - active_loop: appointment_cancellation_form
    steps:
      - action: action_extract_appointment_cancellation_slots  # Extract slots first
      - action: validate_appointment_cancellation_form  # Then validate them
      - active_loop: appointment_cancellation_form # Keep form active while validating

  # Rule to handle user input for appointment_id during appointment cancellation form
  - rule: Handle appointment_id input during appointment cancellation form
    condition:
      - active_loop: appointment_cancellation_form
      - slot_was_set:
          - requested_slot: appointment_id
    steps:
      - intent: inform
      - action: action_extract_appointment_cancellation_slots  # Extract slots from inform intent
      - action: validate_appointment_cancellation_form  # Validate the extracted slots
      - active_loop: appointment_cancellation_form  # Keep the form active

  # Rule to handle user input for phone_number during appointment cancellation form
  - rule: Handle phone_number input during appointment cancellation form
    condition:
      - active_loop: appointment_cancellation_form
      - slot_was_set:
          - requested_slot: phone_number
    steps:
      - intent: inform
      - action: action_extract_appointment_cancellation_slots  # Extract slots from inform intent
      - action: validate_appointment_cancellation_form  # Validate the extracted slots
      - active_loop: appointment_cancellation_form  # Keep the form active

  # Rule to handle form submission when all slots are filled and user confirms
  - rule: Submit appointment cancellation form when complete and confirmed
    condition:
      - active_loop: appointment_cancellation_form
      - slot_was_set:
          - requested_slot: confirm_cancellation
    steps:
      - intent: affirm
      - action: validate_appointment_cancellation_form
      - action: action_submit_appointment_cancellation
      - active_loop: null
      - action: action_listen

  # Rule to handle form submission when all slots are filled but user denies
  - rule: Abort appointment cancellation when user denies
    condition:
      - active_loop: appointment_cancellation_form
      - slot_was_set:
          - requested_slot: confirm_cancellation
    steps:
      - intent: deny
      - action: validate_appointment_cancellation_form
      - action: action_submit_appointment_cancellation  # This will handle the abort logic
      - active_loop: null
      - action: action_listen

  # --- Rules for Appointment Reschedule ---

  # Rule to activate the appointment reschedule form
  - rule: Activate appointment reschedule form
    condition:
      - active_loop: null
    steps:
      - intent: reschedule_appointment
      - action: appointment_reschedule_form
      - active_loop: appointment_reschedule_form

  # Rule for slot extraction and form validation
  - rule: Extract slots and validate appointment reschedule form
    condition:
      - active_loop: appointment_reschedule_form
    steps:
      - action: action_extract_appointment_reschedule_slots
      - action: validate_appointment_reschedule_form
      - active_loop: appointment_reschedule_form

  # Rule to handle user input for reschedule_appointment_id
  - rule: Handle appointment_id input during appointment reschedule form
    condition:
      - active_loop: appointment_reschedule_form
      - slot_was_set:
          - requested_slot: reschedule_appointment_id
    steps:
      - intent: inform
      - action: action_extract_appointment_reschedule_slots
      - action: validate_appointment_reschedule_form
      - active_loop: appointment_reschedule_form

  # Rule to handle user input for reschedule_phone_number
  - rule: Handle phone_number input during appointment reschedule form
    condition:
      - active_loop: appointment_reschedule_form
      - slot_was_set:
          - requested_slot: reschedule_phone_number
    steps:
      - intent: inform
      - action: action_extract_appointment_reschedule_slots
      - action: validate_appointment_reschedule_form
      - active_loop: appointment_reschedule_form

  # Rule to show current appointment after ID and phone verification
  - rule: Show current appointment details for reschedule
    condition:
      - active_loop: appointment_reschedule_form
      - slot_was_set:
          - reschedule_appointment_id
          - reschedule_phone_number
    steps:
      - action: action_show_current_appointment
      - active_loop: appointment_reschedule_form

  # Rule to handle user input for new_appointment_datetime
  - rule: Handle new datetime input during appointment reschedule form
    condition:
      - active_loop: appointment_reschedule_form
      - slot_was_set:
          - requested_slot: new_appointment_datetime
    steps:
      - intent: inform
      - action: action_extract_appointment_reschedule_slots
      - action: validate_appointment_reschedule_form
      - active_loop: appointment_reschedule_form

  # Rule to handle form submission when all slots are filled and user confirms
  - rule: Submit appointment reschedule form when complete and confirmed
    condition:
      - active_loop: appointment_reschedule_form
      - slot_was_set:
          - requested_slot: confirm_reschedule
    steps:
      - intent: affirm
      - action: validate_appointment_reschedule_form
      - action: action_submit_appointment_reschedule
      - active_loop: null
      - action: action_listen

  # Rule to handle form submission when all slots are filled but user denies
  - rule: Abort appointment reschedule when user denies
    condition:
      - active_loop: appointment_reschedule_form
      - slot_was_set:
          - requested_slot: confirm_reschedule
    steps:
      - intent: deny
      - action: validate_appointment_reschedule_form
      - action: action_submit_appointment_reschedule  # This will handle the abort logic
      - active_loop: null
      - action: action_listen


