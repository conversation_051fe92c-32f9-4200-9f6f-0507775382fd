version: "3.1"

stories:
  # Updated happy path story with action-based confirmation
  - story: happy path appointment booking
    steps:
      - intent: greet # User says hi
      - action: utter_greet # <PERSON><PERSON> responds with greeting and asks about service
      - intent: request_appointment # User wants to book
      - action: appointment_form # <PERSON><PERSON> activates the appointment form to collect slots
      - active_loop: appointment_form # The form is now active and collecting required slots
      # User will provide information, triggering 'inform' intent with entities
      - intent: inform # Example: User says "weight loss programs"
        entities:
          - service_type: "Weight Loss Programs"
      - active_loop: appointment_form # Form remains active, asks for next required slot (datetime)
      - intent: inform # Example: User says "tomorrow at 3pm"
        entities:
          - date: "tomorrow"
          - time: "3pm"
      - active_loop: appointment_form # Form remains active, asks for next required slot (name)
      - intent: inform # Example: User says "my name is <PERSON><PERSON>"
        entities:
          - name: "Neel"
      - active_loop: appointment_form # Form remains active, asks for next required slot (phone)
      - intent: inform # Example: User says "my number is 1234567890"
        entities:
          - phone_number: "1234567890"
      - active_loop: null # Form is complete because all required slots are filled
      - action: action_book_appointment

  # Modified story: user provides info upfront
  # This story also ends after the form is complete, with confirmation.
  - story: user provides info upfront
    steps:
      - intent: greet # User says hi
      - action: utter_greet # <PERSON><PERSON> responds
      - intent: inform # User provides service and time in one go
        entities:
          - service_type: "Physical Therapy"
          - date: "next monday"
          - time: "11am"
          - name: "Bob"
          - phone_number: "0987654321"
      - action: appointment_form # Bot activates the form. Since all required slots are already filled...
      - active_loop: null # ...and deactivates.
      - action: action_book_appointment

  # Story: user cancels during booking
  - story: user cancels during booking
    steps:
      - intent: greet
      - action: utter_greet
      - intent: request_appointment
      - action: appointment_form
      - active_loop: appointment_form # User is in the form
      - intent: cancel_appointment # User decides to cancel
      - action: utter_appointment_cancelled # Bot acknowledges cancellation
      - action: utter_goodbye # Bot says goodbye
      - active_loop: null # Important: Exit the active form loop

  # Story: direct cancellation using the new form-based approach
  - story: direct cancellation with form
    steps:
      - intent: greet
      - action: utter_greet
      - intent: cancel_appointment # User directly asks to cancel
      - action: appointment_cancellation_form # Bot activates the cancellation form
      - active_loop: appointment_cancellation_form # Form is now active
      # The form will collect appointment_id and phone_number
      - intent: inform # User provides appointment ID
        entities:
          - appointment_id: "123"
      - action: action_extract_appointment_cancellation_slots # Extract slots
      - action: validate_appointment_cancellation_form # Validate slots
      - active_loop: appointment_cancellation_form # Form remains active
      - intent: inform # User provides phone number
        entities:
          - phone_number: "1234567890"
      - action: action_extract_appointment_cancellation_slots # Extract slots
      - action: validate_appointment_cancellation_form # Validate slots
      - active_loop: appointment_cancellation_form # Form remains active
      - intent: affirm # User confirms cancellation
      - action: validate_appointment_cancellation_form # Validate confirmation
      - action: action_submit_appointment_cancellation # Bot cancels the appointment
      - active_loop: null # Form is deactivated

  # Story: direct cancellation but user aborts
  - story: direct cancellation but user aborts
    steps:
      - intent: greet
      - action: utter_greet
      - intent: cancel_appointment # User directly asks to cancel
      - action: appointment_cancellation_form # Bot activates the cancellation form
      - active_loop: appointment_cancellation_form # Form is now active
      # The form will collect appointment_id and phone_number
      - intent: inform # User provides appointment ID
        entities:
          - appointment_id: "123"
      - action: action_extract_appointment_cancellation_slots # Extract slots
      - action: validate_appointment_cancellation_form # Validate slots
      - active_loop: appointment_cancellation_form # Form remains active
      - intent: inform # User provides phone number
        entities:
          - phone_number: "1234567890"
      - action: action_extract_appointment_cancellation_slots # Extract slots
      - action: validate_appointment_cancellation_form # Validate slots
      - active_loop: appointment_cancellation_form # Form remains active
      - intent: deny # User decides not to cancel
      - action: validate_appointment_cancellation_form # Validate denial
      - action: action_submit_appointment_cancellation # Bot handles the abort
      - active_loop: null # Form is deactivated

  # Keep the default stories or rules for simple interactions to ensure consistency
  - story: say goodbye
    steps:
      - intent: greet
      - action: utter_greet
      - intent: bye
      - action: utter_goodbye

  - story: User says no after being offered help
    steps:
      - intent: greet
      - action: utter_greet
      - intent: deny
      - action: utter_ack_deny

  # Simplified story for appointment verification flow
  - story: happy path appointment verification
    steps:
      - intent: verify_appointment
      - action: appointment_verification_form
      - active_loop: appointment_verification_form
      # The form and rules will handle the rest of the flow

  # Story for handling thank you in a neutral context
  - story: user says thanks in neutral context
    steps:
      - intent: thank_you
      - action: utter_thank_you