version: "3.1"

intents:
  - greet
  - bye
  - affirm
  - deny
  - request_appointment
  - inform
  - cancel_appointment
  - thank_you
  - bot_challenge
  - ask_clinic_address
  - ask_clinic_hours
  - ask_clinic_phone
  - ask_clinic_website
  - ask_clinic_services
  - ask_about_service # Used when user ASKS about a service
  - faq
  - ask_available_times # New intent
  - verify_appointment # Intent for appointment verification

entities:
  - date
  - time
  - service_type
  - name
  - phone_number
  - appointment_id

slots:
  appointment_datetime:
    type: text
    influence_conversation: true
    mappings:
      - type: from_entity
        entity: date
        intent: inform
      - type: from_entity
        entity: time
        intent: inform
      - type: from_text
        intent: None # Fallback mapping

  service_type:
    type: text
    influence_conversation: true
    mappings:
      - type: from_entity
        entity: service_type
        intent: inform # Map from inform intent (when providing service for booking)
      - type: from_entity
        entity: service_type
        intent: ask_about_service # Map from ask_about_service intent (when asking about a service)
      # - type: from_text
      #   intent: None # Keep this mapping as a fallback if user types

  name:
    type: text
    influence_conversation: true
    mappings:
      - type: from_entity
        entity: name
        intent: inform
      - type: from_text
        intent: None # Fallback mapping

  phone_number:
    type: text
    influence_conversation: true
    mappings:
      - type: from_entity
        entity: phone_number
        intent: inform
      # For the appointment verification form, we'll use custom extraction
      # to prevent conflicts with appointment_id

  # --- Temporary slots for date and time parsing across turns ---
  stored_date:
    type: text
    influence_conversation: false # These slots do not influence dialogue flow directly
    mappings:
      - type: custom # Handled by custom validation action

  stored_time:
    type: text
    influence_conversation: false # These slots do not influence dialogue flow directly
    mappings:
      - type: custom # Handled by custom validation action
  # --- End of date/time slots ---

  # --- Slots for name confirmation flow ---
  confirming_name:
    type: bool
    influence_conversation: true
    mappings:
      - type: custom # Set by validate_name action

  name_candidate:
    type: text
    influence_conversation: false
    mappings:
      - type: custom # Set by validate_name action
  # --- End of name confirmation slots ---

  # --- Appointment verification slots ---
  appointment_id:
    type: text
    influence_conversation: true
    mappings:
      - type: custom # Only set by custom action

  # --- Appointment cancellation slots ---
  confirm_cancellation:
    type: bool
    influence_conversation: true
    mappings:
      - type: from_intent
        intent: affirm
        value: true
      - type: from_intent
        intent: deny
        value: false

responses:
  utter_greet:
    - text: "Welcome to Arham Physiotherapy & Rehabilitation Centre! I can help you book an appointment or answer questions about our services. What can I assist you with today?"
    - text: "Hello! Arham Physiotherapy & Rehabilitation Centre's virtual assistant here. I can help with bookings or provide information about our services. How may I help you?"

  utter_ask_service_type:
    - text: "What type of service are you looking to book?"
    - text: "Could you please specify the service you're interested in?"

  utter_ask_appointment_datetime:
    - text: "When would you like to book your appointment?"
    - text: "What date and time works best for you?"

  utter_appointment_booked:
    - text: "Thank you {name}! Your {service_type} appointment is booked for {appointment_datetime}. We will send a confirmation to {phone_number}."

  utter_appointment_cancelled:
    - text: "Okay, I've cancelled your appointment."

  utter_goodbye:
    - text: "Goodbye! Thank you for connecting with Arham Physiotherapy & Rehabilitation Centre."
    - text: "See you later! Feel free to reach out if you have any other questions."

  utter_iamabot:
    - text: "I am a virtual assistant for Arham Physiotherapy & Rehabilitation Centre, here to help you with appointments and information."

  utter_default:
    - text: "Sorry, I didn't understand that. Can you please rephrase?"

  utter_ask_name:
    - text: "What is your full name (first and last name)?"
    - text: "Could you please tell me your full name (first and last name)?"
    - text: "Please enter your full name."

  utter_ask_phone_number:
    - text: "What is your phone number?"
    - text: "Please provide your phone number for appointment confirmation."

  utter_ask_clinic_address:
    - text: "Our address is Arham Physiotherapy & Rehabilitation Centre, 123 Main Street, Mumbai 400001. We're conveniently located near Central Plaza with easy parking access."

  utter_ask_clinic_hours:
    - text: "Our operating hours are Monday to Friday from 8:00 AM to 8:00 PM, and Saturday from 9:00 AM to 5:00 PM. We're closed on Sundays and public holidays."

  utter_ask_clinic_phone:
    - text: "You can call us at +91 22 1234 5678 during our operating hours."

  utter_ask_clinic_website:
    - text: "Our website is www.arhamphysiotherapy.com where you can find more details about our services and team."

  utter_ask_clinic_services:
    - text: "We offer a range of services including: Physical Therapy & Comprehensive Pain Management, Weight Loss Programs, Neurological Rehabilitation, Orthopedic Rehabilitation, and Gynaecology Rehabilitation & Prenatal Care. Would you like details about any specific service?"

  utter_ask_about_service:
    - text: "I'd be happy to tell you more about that service. Which specific service are you interested in learning about?"

  utter_faq:
    - text: "That's a great question! Here are some common FAQs:\n\n- Do you offer walk-ins? Yes, but appointments are preferred.\n- What is your cancellation policy? Please give us 24 hours notice.\n- Do I need a referral? Not necessarily, but it's helpful if you have one.\n- What should I bring? Comfortable clothes, any medical reports, and your ID.\n- Do you accept insurance? Yes, we work with most major insurance providers.\n\nIs there something specific you'd like to know more about?"

  utter_help_options:
    - text: |
        I can help you with the following:
        1. Book an appointment
        2. Reschedule an existing appointment
        3. Cancel an appointment

        What would you like to do next?

  utter_ack_deny:
    - text: "Alright, let me know if you need anything else!"

  utter_affirm:
    - text: "Great! I'll proceed with the booking."
    - text: "Perfect! I'll confirm your appointment."

  utter_deny:
    - text: "I understand. Would you like to try a different time?"
    - text: "No problem. Would you like to choose a different time?"

  utter_ask_date_for_availability:
    - text: "For which date would you like to check availability?"

  utter_availability_on_date:
    - text: "For {date}, available times are: {times_list}. Would you like to book one, or check another date?"

  utter_no_availability_on_date:
    - text: "Sorry, there are no available times on {date}. Please try another date."

  utter_failed_to_check_availability:
    - text: "I'm sorry, I couldn't check the availability right now. Please try again."

  utter_invalid_date_for_availability:
    - text: "I couldn't understand that date. Please provide a valid date like 'tomorrow' or 'July 10th'."

  # Appointment verification responses
  utter_ask_appointment_id:
    - text: "Please provide your appointment ID number."
    - text: "What is your appointment ID?"

  utter_ask_verification_phone:
    - text: "Please provide the phone number you used when booking the appointment."
    - text: "What phone number did you use to book this appointment?"

  utter_verification_success:
    - text: "Thank you! Your appointment has been verified."

  utter_verification_failed_id:
    - text: "I couldn't find an active appointment with that ID. Please check and provide the correct appointment ID."
    - text: "That appointment ID doesn't match any of our active appointments. Please verify the ID and try again."

  utter_verification_failed_phone:
    - text: "The phone number you provided doesn't match our records for this appointment ID. Please try again with the phone number used during booking."

  utter_no_appointment_found:
    - text: "I couldn't find any appointment with the details you provided. Please check your information and try again."

  utter_request_appointment:
    - text: "Sure, I can help you with that. Let's get started."

  utter_inform:
    - text: "Thanks for the information."

  utter_thank_you:
    - text: "You're welcome!"

  utter_you_are_welcome:
    - text: "You're welcome! Is there anything else I can help you with today?"

  # Appointment cancellation responses
  utter_ask_cancellation_appointment_id:
    - text: "Please provide your appointment ID number for cancellation."
    - text: "What is the appointment ID you want to cancel?"

  utter_ask_cancellation_phone:
    - text: "Please provide the phone number you used when booking this appointment."
    - text: "What phone number did you use to book this appointment?"

  utter_confirm_cancellation:
    - text: "Are you sure you want to cancel this appointment? Please confirm with 'yes' or 'no'."
    - text: "Please confirm that you want to cancel this appointment."

  utter_cancellation_success:
    - text: "Your appointment has been successfully cancelled. The time slot is now available for others."
    - text: "I've cancelled your appointment as requested. Is there anything else I can help you with?"

  utter_cancellation_aborted:
    - text: "I've kept your appointment as is. It has not been cancelled."
    - text: "Cancellation aborted. Your appointment is still active."

  utter_cancellation_failed:
    - text: "I'm sorry, there was an issue cancelling your appointment. Please try again or contact our office directly."

actions:
  - action_book_appointment
  - action_cancel_appointment
  - validate_appointment_form
  - action_provide_service_details
  - action_reset_form_state
  - action_check_availability
  - action_submit_appointment_verification
  - validate_appointment_verification_form
  - action_get_appointment_details
  - action_extract_appointment_verification_slots
  - validate_appointment_cancellation_form
  - action_submit_appointment_cancellation
  - action_extract_appointment_cancellation_slots
  - utter_greet
  - utter_goodbye
  - utter_affirm
  - utter_deny
  - utter_request_appointment
  - utter_inform
  - utter_appointment_cancelled
  - utter_thank_you
  - utter_iamabot
  - utter_ask_service_type
  - utter_ask_appointment_datetime
  - utter_appointment_booked
  - utter_default
  - utter_ask_name
  - utter_ask_phone_number
  - utter_ask_clinic_address
  - utter_ask_clinic_hours
  - utter_ask_clinic_phone
  - utter_ask_clinic_website
  - utter_ask_clinic_services
  - utter_ask_about_service
  - utter_faq
  - utter_help_options
  - utter_ack_deny
  - utter_ask_date_for_availability
  - utter_availability_on_date
  - utter_no_availability_on_date
  - utter_failed_to_check_availability
  - utter_invalid_date_for_availability

forms:
  appointment_form:
    required_slots:
      - service_type
      - appointment_datetime
      - name
      - phone_number

  appointment_verification_form:
    required_slots:
      - appointment_id
      - phone_number

  appointment_cancellation_form:
    required_slots:
      - appointment_id
      - phone_number
      - confirm_cancellation

session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: true
