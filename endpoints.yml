# endpoints.yml

# This is the endpoint for the Rasa Action Server.
# It is used when you have custom actions defined in actions.py
# and need to run them.
# For this minimal bot, you don't HAV<PERSON> to run the action server,
# but having the file here prepares you for when you do.
action_endpoint:
  url: http://localhost:5055/webhook

# Uncomment the tracker_store block if you want to save conversation logs.
# By default, conversations are just kept in memory during a session.
# tracker_store:
#   type: redis
#   url: localhost
#   port: 6379
#   db: 0
#   password: <your password>
#   type: postgres
#   url: localhost
#   port: 5432
#   db: <database>
#   username: <username>
#   password: <password>

# Uncomment the event_broker block if you want to connect to an event broker
# event_broker:
#   url: localhost
#   kafka_topic: rasa_core_events
#   type: kafka
#   # ... more broker configuration ...