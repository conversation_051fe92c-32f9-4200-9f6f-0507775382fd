absl-py==1.4.0
aio-pika==8.2.3
aiofiles==24.1.0
aiogram==2.15
aiohttp==3.9.5
aiohttp-retry==2.9.1
aiormq==6.4.2
aiosignal==1.3.2
annotated-types==0.7.0
APScheduler==3.9.1.post1
astunparse==1.6.3
async-timeout==4.0.3
attrs==22.1.0
babel==2.17.0
bidict==0.23.1
blis==0.7.11
boto3==1.38.3
botocore==1.38.3
CacheControl==0.12.14
cachetools==5.5.2
catalogue==2.0.10
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cloudpathlib==0.21.0
cloudpickle==2.2.1
colorclass==2.2.2
coloredlogs==15.0.1
colorhash==1.2.1
confection==0.1.5
confluent-kafka==2.10.0
cryptography==44.0.2
cycler==0.12.1
cymem==2.0.11
Cython==3.0.12
dask==2022.10.2
dateparser==1.2.1
dnspython==2.3.0
docopt==0.6.2
en-core-web-md @ https://github.com/explosion/spacy-models/releases/download/en_core_web_md-3.4.1/en_core_web_md-3.4.1-py3-none-any.whl#sha256=96ddc2e0300e1ed142c2e80bacc83438dc09231f2ca4d104f83afc021eba098e
fbmessenger==6.0.0
filelock==3.18.0
fire==0.7.0
flatbuffers==25.2.10
fonttools==4.57.0
frozenlist==1.6.0
fsspec==2025.3.2
future==1.0.0
gast==0.4.0
transformers>=4.0.0 # For Hugging Face models
torch>=1.8.0 # PyTorch, often a dependency for transformers models
google-auth==2.39.0
google-auth-oauthlib==1.0.0
google-pasta==0.2.0
grpcio==1.71.0
h11==0.16.0
h5py==3.13.0
httptools==0.6.4
huggingface-hub==0.30.2
humanfriendly==10.0
idna==3.10
jax==0.4.30
jaxlib==0.4.30
Jinja2==3.1.6
jmespath==1.0.1
joblib==1.2.0
jsonpickle==3.0.4
jsonschema==4.17.3
keras==2.12.0
kiwisolver==1.4.8
langcodes==3.5.0
language_data==1.3.0
libclang==18.1.1
locket==1.0.0
marisa-trie==1.2.1
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.5.3
mattermostwrapper==2.2
mdurl==0.1.2
ml_dtypes==0.5.1
msgpack==1.1.0
multidict==5.2.0
murmurhash==1.0.12
networkx==2.6.3
numpy==1.23.5
oauthlib==3.2.2
opt_einsum==3.4.0
packaging==20.9
pamqp==3.2.1
partd==1.4.2
pathlib_abc==0.1.1
pathy==0.11.0
pillow==11.2.1
pluggy==1.5.0
portalocker==2.10.1
preshed==3.0.9
prompt-toolkit==3.0.28
propcache==0.3.1
protobuf==4.23.3
psycopg2==2.9.10
psycopg2-binary==2.9.10
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==1.10.9
pydantic_core==2.33.1
pydot==1.4.2
Pygments==2.19.1
PyJWT==2.10.1
pykwalify==1.8.0
pymongo==4.3.3
pyparsing==3.2.3
pyrsistent==0.20.0
python-crfsuite==0.9.11
python-dateutil==2.8.2
python-engineio==4.12.0
python-socketio==5.13.0
pytz==2022.7.1
PyYAML==6.0
questionary==1.10.0
randomname==0.1.5
rasa==3.6.18
rasa-sdk==3.6.2
redis==4.6.0
regex==2022.10.31
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==14.0.0
rocketchat-API==1.30.0
rsa==4.9.1
ruamel.yaml==0.17.21
ruamel.yaml.clib==0.2.12
s3transfer==0.12.0
safetensors==0.4.5
sanic==21.12.2
Sanic-Cors==2.0.1
sanic-jwt==1.8.0
sanic-routing==0.7.2
scikit-learn==1.1.3
scipy==1.10.1
sentry-sdk==1.14.0
shellingham==1.5.4
simple-websocket==1.1.0
six==1.17.0
sklearn-crfsuite==0.3.6
skops==0.9.0
slack_sdk==3.35.0
smart-open==6.4.0
spacy==3.4.4
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SQLAlchemy==1.4.54
srsly==2.5.1
structlog==23.3.0
structlog-sentry==2.1.0
tabulate==0.9.0
tarsafe==0.0.4
tensorboard==2.12.3
tensorboard-data-server==0.7.2
tensorflow-estimator==2.12.0
tensorflow-hub==0.13.0
tensorflow-macos==2.12.0
termcolor==3.0.1
terminaltables==3.1.10
thinc==8.1.12
threadpoolctl==3.6.0
toolz==1.0.0
tqdm==4.67.1
twilio==8.2.2
typer==0.7.0
typing-inspection==0.4.0
typing-utils==0.1.0
typing_extensions==4.13.2
tzlocal==5.3.1
ujson==5.10.0
urllib3==2.4.0
uvloop==0.21.0
wasabi==0.10.1
wcwidth==0.2.13
weasel==0.4.1
webexteamssdk==1.6.1
websockets==10.4
Werkzeug==3.1.3
wrapt==1.14.1
wsproto==1.2.0
yarl==1.20.0
